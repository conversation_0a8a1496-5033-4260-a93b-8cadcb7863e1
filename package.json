{"name": "TTS", "description": "Trips Tracking System ", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "lint-staged": "lint-staged", "prepare": "husky", "test": "vitest", "test:coverage": "vitest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@googlemaps/markerclusterer": "^2.6.2", "@microsoft/signalr": "^8.0.7", "@microsoft/signalr-protocol-msgpack": "^9.0.6", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/vite": "^4.1.11", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@vis.gl/react-google-maps": "^1.5.5", "axios": "^1.11.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "file-saver": "^2.0.5", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "lucide-react": "^0.525.0", "primereact": "^10.9.6", "quill": "^2.0.3", "react": "^18.3.1", "react-day-picker": "^9.8.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-i18next": "^15.6.0", "react-icons": "^5.4.0", "react-resizable-panels": "^3.0.3", "react-router-dom": "^7.6.3", "recharts": "^2.15.4", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "xlsx": "^0.18.5", "zod": "^4.0.14", "zustand": "^5.0.6"}, "devDependencies": {"@chromatic-com/storybook": "^4.1.1", "@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/css": "^0.10.0", "@eslint/js": "^9.32.0", "@eslint/json": "^0.13.1", "@eslint/markdown": "^7.1.0", "@storybook/addon-a11y": "^9.1.3", "@storybook/addon-docs": "^9.1.3", "@storybook/addon-onboarding": "^9.1.3", "@storybook/addon-vitest": "^9.1.3", "@storybook/react-vite": "^9.1.3", "@types/file-saver": "^2.0.7", "@types/google.maps": "^3.58.1", "@types/node": "^24.0.13", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/xlsx": "^0.0.35", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "@vitejs/plugin-react": "^4.6.0", "@vitest/browser": "3.2.4", "@vitest/coverage-v8": "3.2.4", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-storybook": "^9.1.3", "globals": "^16.3.0", "husky": "^9.1.7", "jiti": "^2.5.1", "lint-staged": "^16.1.4", "playwright": "^1.54.2", "prettier": "^3.6.2", "storybook": "^9.1.3", "tw-animate-css": "^1.3.5", "typescript": "~5.8.3", "typescript-eslint": "^8.39.0", "vite": "^7.0.4", "vite-plugin-svg-icons": "^2.0.1", "vitest": "^3.2.4"}}