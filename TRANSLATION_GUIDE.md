# Translation Guide

This application supports multi-language functionality using `react-i18next`. The system supports English (en) and Arabic (ar) languages with RTL (Right-to-Left) support for Arabic.

## Features

- ✅ Multi-language support (English & Arabic)
- ✅ RTL layout support for Arabic
- ✅ Language persistence in localStorage
- ✅ Automatic language detection
- ✅ Easy language switching via UI
- ✅ Context-based language management

## How to Use

### 1. Basic Translation Usage

```tsx
import { useTranslation } from "react-i18next";

function MyComponent() {
  const { t } = useTranslation();

  return (
    <div>
      <h1>{t("navbar.reports")}</h1>
      <p>{t("common.loading")}</p>
    </div>
  );
}
```

### 2. Using the Custom Hook

```tsx
import { useTranslations } from "@/hooks/useTranslations";

function MyComponent() {
  const { t, currentLanguage, switchLanguage, isRTL } = useTranslations();

  return (
    <div>
      <h1>{t("navbar.reports")}</h1>
      <button onClick={() => switchLanguage("ar")}>Switch to Arabic</button>
      <p>Current language: {currentLanguage}</p>
      <p>Is RTL: {isRTL ? "Yes" : "No"}</p>
    </div>
  );
}
```

### 3. Language Switching

The language can be switched using the language dropdown in the navbar, or programmatically:

```tsx
const { switchLanguage } = useTranslations();

// Switch to Arabic
switchLanguage("ar");

// Switch to English
switchLanguage("en");
```

## Translation Files

Translation keys are organized in JSON files located in `src/i18n/locales/`:

- `en.json` - English translations
- `ar.json` - Arabic translations

### Adding New Translations

1. Add the translation key to both language files:

```json
// en.json
{
  "newSection": {
    "title": "New Title",
    "description": "New Description"
  }
}

// ar.json
{
  "newSection": {
    "title": "عنوان جديد",
    "description": "وصف جديد"
  }
}
```

2. Use the translation in your component:

```tsx
const { t } = useTranslation();
return <h1>{t("newSection.title")}</h1>;
```

## RTL Support

The application automatically handles RTL layout for Arabic:

- Document direction is set to `rtl` for Arabic
- CSS classes with `[dir="rtl"]` selector provide RTL-specific styles
- Flexbox directions are automatically reversed
- Text alignment is adjusted for RTL

## File Structure

```
src/
├── i18n/
│   ├── index.ts              # i18n configuration
│   └── locales/
│       ├── en.json           # English translations
│       └── ar.json           # Arabic translations
├── contexts/
│   └── LanguageContext.tsx   # Language context provider
├── hooks/
│   └── useTranslations.ts    # Custom translation hook
└── assets/
    └── styles/
        └── rtl.css           # RTL-specific styles
```

## Available Translation Keys

### Navbar

- `navbar.reports` - Reports dropdown
- `navbar.settings` - Settings link
- `navbar.ports` - Ports link
- `navbar.myPorts` - My Ports link
- `navbar.arrivalTracking` - Arrival Tracking link
- `navbar.dashboard` - Dashboard link
- `navbar.trips` - Trips link
- `navbar.monitoring` - Monitoring link
- `navbar.language` - Language dropdown label
- `navbar.logout` - Logout button
- `navbar.username` - Username label

### Reports

- `reports.trips` - Trips report
- `reports.alerts` - Alerts report
- `reports.records` - Records report
- `reports.statistics` - Statistics report
- `reports.stops` - Stops report
- `reports.employees` - Employees report
- `reports.portDistribution` - Port Distribution report
- `reports.tripTracking` - Trip Tracking report
- `reports.activeTrips` - Active Trips report

### Common

- `common.loading` - Loading text
- `common.error` - Error text
- `common.success` - Success text
- `common.cancel` - Cancel button
- `common.save` - Save button
- `common.edit` - Edit button
- `common.delete` - Delete button
- `common.add` - Add button
- `common.search` - Search placeholder
- `common.filter` - Filter button
- `common.clear` - Clear button

### Pages

- `pages.monitorBoard` - Monitor Board page title
- `pages.ports` - Ports page title
- `pages.login` - Login page title
- `pages.notFound` - 404 page title

### Login

- `login.welcomeBack` - Welcome back message
- `login.login` - Login title
- `login.username` - Username placeholder
- `login.password` - Password placeholder
- `login.loginButton` - Login button text

## Best Practices

1. **Use nested keys** for better organization (e.g., `navbar.reports`)
2. **Keep translations consistent** across components
3. **Use the custom hook** for easier access to language state
4. **Test both languages** to ensure proper RTL layout
5. **Add new translations** to both language files simultaneously
6. **Use semantic keys** that describe the content, not the location

## Troubleshooting

### Translation not showing

- Check if the key exists in both language files
- Verify the key path is correct
- Ensure the component is wrapped in `LanguageProvider`

### RTL layout issues

- Check if RTL CSS is properly imported
- Verify document direction is set correctly
- Test with different screen sizes

### Language not persisting

- Check localStorage permissions
- Verify the language detection configuration
- Ensure the language context is properly initialized
