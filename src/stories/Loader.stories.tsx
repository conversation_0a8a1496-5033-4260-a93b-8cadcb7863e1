import type { Meta, StoryObj } from '@storybook/react';

import Loader from '@/components/common/ui/Loader';

const meta: Meta<typeof Loader> = {
    title: 'Components/Loader',
    component: Loader,
    tags: ['autodocs'],
    argTypes: {
        isLoading: {
            description: 'Show loading state',
            control: 'boolean',
        },
    },
};

export default meta;
type Story = StoryObj<typeof Loader>;

export const Default: Story = {
    args: {
        isLoading: true,
    },
};

export const NotLoading: Story = {
    args: {
        isLoading: false,
    },
};
