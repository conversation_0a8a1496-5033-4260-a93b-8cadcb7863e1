import { useState } from 'react';
import type { Meta, StoryObj } from '@storybook/react';

import { Switch } from '@/components/common/ui/Switch';

const meta: Meta<typeof Switch> = {
    title: 'Components/Switch',
    component: Switch,
    tags: ['autodocs'],
    argTypes: {
        checked: {
            description: 'Switch state',
            control: 'boolean',
        },
        disabled: {
            description: 'Disable switch',
            control: 'boolean',
        },
        onCheckedChange: {
            description: 'Change handler',
            action: 'changed',
        },
    },
};

export default meta;
type Story = StoryObj<typeof Switch>;

export const Default: Story = {
    args: {},
};

export const Checked: Story = {
    args: {
        checked: true,
    },
};

export const Disabled: Story = {
    args: {
        disabled: true,
    },
};

export const DisabledChecked: Story = {
    args: {
        checked: true,
        disabled: true,
    },
};

export const Controlled = () => {
    const [checked, setChecked] = useState(false);
    return <Switch checked={checked} onCheckedChange={setChecked} />;
};
Controlled.storyName = 'Controlled Switch';
