import type { <PERSON>a, StoryObj } from '@storybook/react';

import { Tag } from '@/components/common/ui/Tag';

const meta: Meta<typeof Tag> = {
    title: 'Components/Tag',
    component: Tag,
    tags: ['autodocs'],
    argTypes: {
        children: {
            description: 'Tag content',
            control: 'text',
        },
        bgColor: {
            description: 'Background color class',
            control: 'select',
            options: ['bg-gray-100', 'bg-blue-100', 'bg-red-100', 'bg-green-100'],
        },
        textColor: {
            description: 'Text color class',
            control: 'select',
            options: ['text-gray-700', 'text-blue-700', 'text-red-700', 'text-green-700'],
        },
    },
};

export default meta;
type Story = StoryObj<typeof Tag>;

export const Default: Story = {
    args: {
        children: 'Tag',
    },
};

export const Secondary: Story = {
    args: {
        children: 'Secondary',
        bgColor: 'bg-blue-100',
        textColor: 'text-blue-700',
    },
};

export const Destructive: Story = {
    args: {
        children: 'Destructive',
        bgColor: 'bg-red-100',
        textColor: 'text-red-700',
    },
};

export const Outline: Story = {
    args: {
        children: 'Outline',
        bgColor: 'bg-green-100',
        textColor: 'text-green-700',
    },
};
