import type { Meta, StoryObj } from '@storybook/react';

import LineChart from '@/components/common/ui/LineChart';

const meta = {
    title: 'Components/LineChart',
    component: LineChart,
    tags: ['autodocs'],
    argTypes: {
        chartConfig: {
            description: 'Chart configuration',
            control: 'object',
        },
        chartData: {
            description: 'Chart data',
            control: 'object',
        },
        dataKey: {
            description: 'Data key for X-axis',
            control: 'text',
        },
    },
} satisfies Meta<typeof LineChart>;

export default meta;
type Story = StoryObj<typeof meta>;

const sampleData = [
    { name: 'Jan', value: 400 },
    { name: 'Feb', value: 300 },
    { name: 'Mar', value: 600 },
    { name: 'Apr', value: 800 },
    { name: 'May', value: 500 },
    { name: 'Jun', value: 700 },
];

const sampleConfig = {
    value: { color: '#3b82f6' },
};

export const Default: Story = {
    args: {
        chartConfig: sampleConfig,
        chartData: sampleData,
        dataKey: 'name',
    },
};
