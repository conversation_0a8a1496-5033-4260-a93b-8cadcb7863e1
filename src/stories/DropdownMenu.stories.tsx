import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/common/ui/DropdownMenu';

const meta: Meta<typeof DropdownMenu> = {
    title: 'Components/DropdownMenu',
    component: DropdownMenu,
    tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof DropdownMenu>;

export const Default: Story = {
    render: () => (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <button className="px-4 py-2 bg-blue-500 text-white rounded">Open Menu</button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
                <DropdownMenuItem>Profile</DropdownMenuItem>
                <DropdownMenuItem>Settings</DropdownMenuItem>
                <DropdownMenuItem>Help</DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>Logout</DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    ),
};

export const WithLabels: Story = {
    render: () => (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <button className="px-4 py-2 bg-green-500 text-white rounded">Actions</button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
                <DropdownMenuLabel>User Actions</DropdownMenuLabel>
                <DropdownMenuItem>Edit Profile</DropdownMenuItem>
                <DropdownMenuItem>Change Password</DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuLabel>System</DropdownMenuLabel>
                <DropdownMenuItem>Settings</DropdownMenuItem>
                <DropdownMenuItem>Logout</DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    ),
};

export const WithIcons: Story = {
    render: () => (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <button className="px-4 py-2 bg-purple-500 text-white rounded">More Options</button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
                <DropdownMenuItem>
                    <span className="mr-2">📝</span>
                    Edit
                </DropdownMenuItem>
                <DropdownMenuItem>
                    <span className="mr-2">📋</span>
                    Copy
                </DropdownMenuItem>
                <DropdownMenuItem>
                    <span className="mr-2">🗑️</span>
                    Delete
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    ),
};
