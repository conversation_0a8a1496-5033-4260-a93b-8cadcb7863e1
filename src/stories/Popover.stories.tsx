import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';

import { Popover, PopoverContent, PopoverTrigger } from '@/components/common/ui/Popover';

const meta: Meta<typeof Popover> = {
    title: 'Components/Popover',
    component: Popover,
    tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof Popover>;

export const Default: Story = {
    render: () => (
        <Popover>
            <PopoverTrigger>Click me</PopoverTrigger>
            <PopoverContent>
                <div className="p-4">
                    <h4 className="font-medium">Popover content</h4>
                    <p className="text-sm text-gray-600">This is popover content</p>
                </div>
            </PopoverContent>
        </Popover>
    ),
};

export const WithButton: Story = {
    render: () => (
        <Popover>
            <PopoverTrigger asChild>
                <button className="px-4 py-2 bg-blue-500 text-white rounded">Open popover</button>
            </PopoverTrigger>
            <PopoverContent>
                <div className="p-4">
                    <h4 className="font-medium">Settings</h4>
                    <p className="text-sm text-gray-600">Configure your preferences</p>
                </div>
            </PopoverContent>
        </Popover>
    ),
};

export const WithForm: Story = {
    render: () => (
        <Popover>
            <PopoverTrigger asChild>
                <button className="px-4 py-2 bg-green-500 text-white rounded">Add item</button>
            </PopoverTrigger>
            <PopoverContent>
                <div className="p-4 space-y-3">
                    <h4 className="font-medium">Add new item</h4>
                    <input type="text" placeholder="Item name" className="w-full px-3 py-2 border rounded" />
                    <button className="w-full px-3 py-2 bg-blue-500 text-white rounded">Add</button>
                </div>
            </PopoverContent>
        </Popover>
    ),
};
