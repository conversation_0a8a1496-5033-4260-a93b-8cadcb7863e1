import type { <PERSON>a, StoryObj } from '@storybook/react';

import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/common/ui/Resizable';

const meta = {
    title: 'Components/Resizable',
    component: ResizablePanelGroup,
    parameters: {
        layout: 'centered',
    },
    tags: ['autodocs'],
} satisfies Meta<typeof ResizablePanelGroup>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    args: {
        direction: 'horizontal',
    },
    render: (args) => (
        <div className="h-[200px] w-[400px] rounded-lg border">
            <ResizablePanelGroup {...args}>
                <ResizablePanel defaultSize={50}>
                    <div className="flex h-full items-center justify-center p-6">
                        <span className="font-semibold">Panel 1</span>
                    </div>
                </ResizablePanel>
                <ResizableHandle />
                <ResizablePanel defaultSize={50}>
                    <div className="flex h-full items-center justify-center p-6">
                        <span className="font-semibold">Panel 2</span>
                    </div>
                </ResizablePanel>
            </ResizablePanelGroup>
        </div>
    ),
};

export const Vertical: Story = {
    args: {
        direction: 'vertical',
    },
    render: (args) => (
        <div className="h-[400px] w-[200px] rounded-lg border">
            <ResizablePanelGroup {...args}>
                <ResizablePanel defaultSize={50}>
                    <div className="flex h-full items-center justify-center p-6">
                        <span className="font-semibold">Panel 1</span>
                    </div>
                </ResizablePanel>
                <ResizableHandle />
                <ResizablePanel defaultSize={50}>
                    <div className="flex h-full items-center justify-center p-6">
                        <span className="font-semibold">Panel 2</span>
                    </div>
                </ResizablePanel>
            </ResizablePanelGroup>
        </div>
    ),
};
