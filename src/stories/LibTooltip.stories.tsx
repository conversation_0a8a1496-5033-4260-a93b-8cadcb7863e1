import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import { LibTooltip, TooltipTrigger, TooltipContent } from '@/components/common/ui/LibTooltip';

const meta: Meta<typeof LibTooltip> = {
    title: 'Components/LibTooltip',
    component: LibTooltip,
    tags: ['autodocs'],
    argTypes: {
        children: {
            description: 'Trigger element',
            control: 'text',
        },
    },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    args: {
        children: 'Hover me',
    },
    render: (args) => (
        <LibTooltip>
            <TooltipTrigger>{args.children}</TooltipTrigger>
            <TooltipContent>Tooltip content</TooltipContent>
        </LibTooltip>
    ),
};

export const WithButton: Story = {
    args: {
        children: <button className="px-4 py-2 bg-blue-500 text-white rounded">Button</button>,
    },
    render: (args) => (
        <LibTooltip>
            <TooltipTrigger>{args.children}</TooltipTrigger>
            <TooltipContent>Button tooltip</TooltipContent>
        </LibTooltip>
    ),
};

export const LongContent: Story = {
    args: {
        children: 'Long tooltip',
    },
    render: (args) => (
        <LibTooltip>
            <TooltipTrigger>{args.children}</TooltipTrigger>
            <TooltipContent>
                This is a longer tooltip with more detailed information that spans multiple lines
            </TooltipContent>
        </LibTooltip>
    ),
};
