import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/common/ui/Dialog';

const meta: Meta<typeof Dialog> = {
    title: 'Components/Dialog',
    component: Dialog,
    tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof Dialog>;

export const Default: Story = {
    render: () => (
        <Dialog>
            <DialogTrigger asChild>
                <button className="px-4 py-2 bg-blue-500 text-white rounded">Open Dialog</button>
            </DialogTrigger>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>Dialog Title</DialogTitle>
                    <DialogDescription>Dialog description goes here.</DialogDescription>
                </DialogHeader>
                <div className="py-4">
                    <p>Dialog content goes here.</p>
                </div>
                <DialogFooter>
                    <button className="px-4 py-2 bg-gray-500 text-white rounded mr-2">Cancel</button>
                    <button className="px-4 py-2 bg-blue-500 text-white rounded">Save</button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    ),
};

export const Confirmation: Story = {
    render: () => (
        <Dialog>
            <DialogTrigger asChild>
                <button className="px-4 py-2 bg-red-500 text-white rounded">Delete Item</button>
            </DialogTrigger>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>Confirm Deletion</DialogTitle>
                    <DialogDescription>
                        Are you sure you want to delete this item? This action cannot be undone.
                    </DialogDescription>
                </DialogHeader>
                <DialogFooter>
                    <button className="px-4 py-2 bg-gray-500 text-white rounded mr-2">Cancel</button>
                    <button className="px-4 py-2 bg-red-500 text-white rounded">Delete</button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    ),
};

export const Form: Story = {
    render: () => (
        <Dialog>
            <DialogTrigger asChild>
                <button className="px-4 py-2 bg-green-500 text-white rounded">Add User</button>
            </DialogTrigger>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle>Add New User</DialogTitle>
                    <DialogDescription>Enter the user details below.</DialogDescription>
                </DialogHeader>
                <div className="py-4 space-y-4">
                    <div>
                        <label className="block text-sm font-medium mb-1">Name</label>
                        <input type="text" className="w-full px-3 py-2 border rounded" placeholder="Enter name" />
                    </div>
                    <div>
                        <label className="block text-sm font-medium mb-1">Email</label>
                        <input type="email" className="w-full px-3 py-2 border rounded" placeholder="Enter email" />
                    </div>
                </div>
                <DialogFooter>
                    <button className="px-4 py-2 bg-gray-500 text-white rounded mr-2">Cancel</button>
                    <button className="px-4 py-2 bg-green-500 text-white rounded">Add User</button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    ),
};
