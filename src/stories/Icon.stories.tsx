import type { <PERSON>a, StoryObj } from '@storybook/react';

import { Icon, type IconName } from '@/components/common/ui/Icon';

const meta: Meta<typeof Icon> = {
    title: 'Components/Icon',
    component: Icon,
    tags: ['autodocs'],
    parameters: {
        docs: {
            description: {
                component: `
# Icon Component

A reusable SVG-based Icon component that integrates with \`vite-plugin-svg-icons\`.

## Features:
- Dynamically renders an SVG \`<use>\` reference from the sprite generated by \`vite-plugin-svg-icons\`
- Supports custom \`className\` for styling (size, color, margin, etc.)
- Defaults to a consistent style if no custom class is provided
- Uses \`currentColor\` for \`fill\`, making the icon automatically adapt to the text color

## Usage:
\`\`\`tsx
<Icon name="truck" />
<Icon name="alert" className="w-8 h-8 text-red-600" />
<Icon name="filter" className="w-6 h-6" />
\`\`\`
                `,
            },
        },
    },
    argTypes: {
        name: {
            description: 'Icon name - must match the SVG filename without extension',
            control: 'select',
            options: [
                // Map & Navigation Icons
                'truck',
                'tripPin',
                'checkPointPin',
                'policeCheckPointPin',
                'seaPortPin',
                'airPortPin',
                'landPortPin',
                'enteringGeoFence',
                'leavingGeoFence',
                'shipmentTracking',

                // Battery & Charging Icons
                'batteryEmpty',
                'batteryVeryLow',
                'batteryLow',
                'batteryMedium',
                'batteryFull',
                'batteryCharging',
                'charger',
                'chargerOn',
                'charging',

                // Signal & GPS Icons
                'gpsDisconnected',
                'gpsSignal',
                'gpsSignalFull',
                'gpsSignalWeak',
                'signalNo',
                'signalLow',
                'signalLowMedium',
                'signalMedium',
                'signalFull',

                // Status & Alert Icons
                'alert',
                'filter',
                'speed',
            ],
        },
        className: {
            description: 'Additional CSS classes for styling (size, color, etc.)',
            control: 'text',
        },
    },
};

export default meta;
type Story = StoryObj<typeof Icon>;

// Basic Usage Stories
export const Default: Story = {
    args: {
        name: 'truck',
    },
};

export const Small: Story = {
    args: {
        name: 'truck',
        className: 'w-4 h-4',
    },
};

export const Large: Story = {
    args: {
        name: 'truck',
        className: 'w-8 h-8',
    },
};

export const WithCustomColor: Story = {
    args: {
        name: 'alert',
        className: 'w-6 h-6 text-red-500',
    },
};

// Map & Navigation Icons
export const MapIcons: Story = {
    render: () => (
        <div className="grid grid-cols-4 gap-4 p-4">
            <div className="flex flex-col items-center gap-2">
                <Icon name="truck" className="w-8 h-8" />
                <span className="text-sm">Truck</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="tripPin" className="w-8 h-8" />
                <span className="text-sm">Trip Pin</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="checkPointPin" className="w-8 h-8" />
                <span className="text-sm">Checkpoint</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="policeCheckPointPin" className="w-8 h-8" />
                <span className="text-sm">Police Station</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="seaPortPin" className="w-8 h-8" />
                <span className="text-sm">Sea Port</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="airPortPin" className="w-8 h-8" />
                <span className="text-sm">Air Port</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="landPortPin" className="w-8 h-8" />
                <span className="text-sm">Land Port</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="enteringGeoFence" className="w-8 h-8" />
                <span className="text-sm">Entering GeoFence</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="leavingGeoFence" className="w-8 h-8" />
                <span className="text-sm">Leaving GeoFence</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="shipmentTracking" className="w-8 h-8" />
                <span className="text-sm">Shipment Tracking</span>
            </div>
        </div>
    ),
    parameters: {
        docs: {
            description: {
                story: 'Map and navigation related icons used for tracking and location purposes.',
            },
        },
    },
};

// Battery & Charging Icons
export const BatteryIcons: Story = {
    render: () => (
        <div className="grid grid-cols-4 gap-4 p-4">
            <div className="flex flex-col items-center gap-2">
                <Icon name="batteryEmpty" className="w-8 h-8 text-red-500" />
                <span className="text-sm">Empty</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="batteryVeryLow" className="w-8 h-8 text-red-400" />
                <span className="text-sm">Very Low</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="batteryLow" className="w-8 h-8 text-orange-500" />
                <span className="text-sm">Low</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="batteryMedium" className="w-8 h-8 text-yellow-500" />
                <span className="text-sm">Medium</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="batteryFull" className="w-8 h-8 text-green-500" />
                <span className="text-sm">Full</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="batteryCharging" className="w-8 h-8 text-blue-500" />
                <span className="text-sm">Charging</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="charger" className="w-8 h-8" />
                <span className="text-sm">Charger</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="chargerOn" className="w-8 h-8 text-green-500" />
                <span className="text-sm">Charger On</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="charging" className="w-8 h-8 text-blue-500" />
                <span className="text-sm">Charging</span>
            </div>
        </div>
    ),
    parameters: {
        docs: {
            description: {
                story: 'Battery status and charging related icons with appropriate color coding.',
            },
        },
    },
};

// Signal & GPS Icons
export const SignalIcons: Story = {
    render: () => (
        <div className="grid grid-cols-4 gap-4 p-4">
            <div className="flex flex-col items-center gap-2">
                <Icon name="gpsDisconnected" className="w-8 h-8 text-red-500" />
                <span className="text-sm">GPS Disconnected</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="gpsSignal" className="w-8 h-8 text-blue-500" />
                <span className="text-sm">GPS Signal</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="gpsSignalFull" className="w-8 h-8 text-green-500" />
                <span className="text-sm">GPS Full</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="gpsSignalWeak" className="w-8 h-8 text-yellow-500" />
                <span className="text-sm">GPS Weak</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="signalNo" className="w-8 h-8 text-red-500" />
                <span className="text-sm">No Signal</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="signalLow" className="w-8 h-8 text-red-400" />
                <span className="text-sm">Low Signal</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="signalLowMedium" className="w-8 h-8 text-orange-500" />
                <span className="text-sm">Low-Medium</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="signalMedium" className="w-8 h-8 text-yellow-500" />
                <span className="text-sm">Medium</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="signalFull" className="w-8 h-8 text-green-500" />
                <span className="text-sm">Full Signal</span>
            </div>
        </div>
    ),
    parameters: {
        docs: {
            description: {
                story: 'GPS and signal strength indicators with color-coded status levels.',
            },
        },
    },
};

// Status & Alert Icons
export const StatusIcons: Story = {
    render: () => (
        <div className="grid grid-cols-3 gap-4 p-4">
            <div className="flex flex-col items-center gap-2">
                <Icon name="alert" className="w-8 h-8 text-red-500" />
                <span className="text-sm">Alert</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="filter" className="w-8 h-8" />
                <span className="text-sm">Filter</span>
            </div>
            <div className="flex flex-col items-center gap-2">
                <Icon name="speed" className="w-8 h-8" />
                <span className="text-sm">Speed</span>
            </div>
        </div>
    ),
    parameters: {
        docs: {
            description: {
                story: 'General status and utility icons for alerts, filtering, and speed indicators.',
            },
        },
    },
};

// All Icons Overview
export const AllIcons: Story = {
    render: () => {
        const allIcons = [
            'truck',
            'tripPin',
            'checkPointPin',
            'policeCheckPointPin',
            'seaPortPin',
            'airPortPin',
            'landPortPin',
            'enteringGeoFence',
            'leavingGeoFence',
            'shipmentTracking',
            'batteryEmpty',
            'batteryVeryLow',
            'batteryLow',
            'batteryMedium',
            'batteryFull',
            'batteryCharging',
            'charger',
            'chargerOn',
            'charging',
            'gpsDisconnected',
            'gpsSignal',
            'gpsSignalFull',
            'gpsSignalWeak',
            'signalNo',
            'signalLow',
            'signalLowMedium',
            'signalMedium',
            'signalFull',
            'alert',
            'filter',
            'speed',
        ];

        return (
            <div className="grid grid-cols-6 gap-4 p-4">
                {allIcons.map((iconName) => (
                    <div key={iconName} className="flex flex-col items-center gap-2 p-2 border rounded">
                        <Icon name={iconName as IconName} className="w-6 h-6" />
                        <span className="text-xs text-center">{iconName}</span>
                    </div>
                ))}
            </div>
        );
    },
    parameters: {
        docs: {
            description: {
                story: 'Complete overview of all available icons in the system.',
            },
        },
    },
};
