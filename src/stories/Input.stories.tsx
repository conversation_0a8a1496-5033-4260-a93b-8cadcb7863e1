import type { Meta, StoryObj } from '@storybook/react';

import { Input } from '@/components/common/ui/Input';

const meta: Meta<typeof Input> = {
    title: 'Components/Input',
    component: Input,
    tags: ['autodocs'],
    argTypes: {
        type: {
            description: 'Input type',
            control: 'select',
            options: ['text', 'email', 'password', 'number', 'tel', 'url'],
        },
        placeholder: {
            description: 'Placeholder text',
            control: 'text',
        },
        disabled: {
            description: 'Disable input',
            control: 'boolean',
        },
    },
};

export default meta;
type Story = StoryObj<typeof Input>;

export const Default: Story = {
    args: {
        placeholder: 'Enter text...',
    },
};

export const Email: Story = {
    args: {
        type: 'email',
        placeholder: 'Enter email...',
    },
};

export const Password: Story = {
    args: {
        type: 'password',
        placeholder: 'Enter password...',
    },
};

export const Disabled: Story = {
    args: {
        placeholder: 'Disabled input',
        disabled: true,
    },
};

export const WithValue: Story = {
    args: {
        value: 'Pre-filled value',
    },
};
