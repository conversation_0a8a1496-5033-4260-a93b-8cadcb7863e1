import { useState } from 'react';
import type { Meta, StoryObj } from '@storybook/react';

import { DatePicker } from '@/components/common/ui/DatePicker';

const meta: Meta<typeof DatePicker> = {
    title: 'Components/DatePicker',
    component: DatePicker,
    tags: ['autodocs'],
    argTypes: {
        placeholder: {
            description: 'Input placeholder',
            control: 'text',
        },
        disabled: {
            description: 'Disable picker',
            control: 'boolean',
        },
    },
};

export default meta;
type Story = StoryObj<typeof DatePicker>;

export const Default: Story = {
    args: {
        placeholder: 'Pick a date',
    },
};

export const WithDefaultDate: Story = {
    args: {
        placeholder: 'Pick a date',
        value: new Date(),
    },
};

export const Disabled: Story = {
    args: {
        placeholder: 'Pick a date',
        disabled: true,
    },
};

export const Controlled = () => {
    const [date, setDate] = useState<Date>();

    return <DatePicker placeholder="Pick a date" value={date} onChange={(date) => setDate(date || undefined)} />;
};
Controlled.storyName = 'Controlled DatePicker';
