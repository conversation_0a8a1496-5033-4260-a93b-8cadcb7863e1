import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';
import { fn } from 'storybook/test';

// Import the actual Button component from your UI library
import { Button } from '@/components/common/ui/Button';

// Mock icons for the stories
const DownloadIcon = () => (
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
        <polyline points="7,10 12,15 17,10" />
        <line x1="12" y1="15" x2="12" y2="3" />
    </svg>
);

const FilterIcon = () => (
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <polygon points="22,3 2,3 10,12.46 10,19 14,21 14,12.46 22,3" />
    </svg>
);

const ArrowLeftIcon = () => (
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <line x1="19" y1="12" x2="5" y2="12" />
        <polyline points="12,19 5,12 12,5" />
    </svg>
);

const StatsIcon = () => (
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <line x1="18" y1="20" x2="18" y2="10" />
        <line x1="12" y1="20" x2="12" y2="4" />
        <line x1="6" y1="20" x2="6" y2="14" />
    </svg>
);

const CalendarIcon = () => (
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
        <line x1="16" y1="2" x2="16" y2="6" />
        <line x1="8" y1="2" x2="8" y2="6" />
        <line x1="3" y1="10" x2="21" y2="10" />
    </svg>
);

const LoaderIcon = () => (
    <svg
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        className="animate-spin">
        <line x1="12" y1="2" x2="12" y2="6" />
        <line x1="12" y1="18" x2="12" y2="22" />
        <line x1="4.93" y1="4.93" x2="7.76" y2="7.76" />
        <line x1="16.24" y1="16.24" x2="19.07" y2="19.07" />
        <line x1="2" y1="12" x2="6" y2="12" />
        <line x1="18" y1="12" x2="22" y2="12" />
        <line x1="4.93" y1="19.07" x2="7.76" y2="16.24" />
        <line x1="16.24" y1="7.76" x2="19.07" y2="4.93" />
    </svg>
);

const ErrorIcon = () => (
    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
        <circle cx="12" cy="12" r="10" />
        <line x1="15" y1="9" x2="9" y2="15" />
        <line x1="9" y1="9" x2="15" y2="15" />
    </svg>
);

const meta = {
    title: 'Components/Button',
    component: Button,
    parameters: {
        layout: 'centered',
        docs: {
            description: {
                component: `
<div style="max-height: 300px; overflow-y: auto; padding-right: 8px;">

<h1>Button Component</h1>

A versatile button component built with <strong>class-variance-authority (CVA)</strong> for consistent styling and behavior across the application.

<h2>Usage in Project</h2>

<h3>1. Alerts Page</h3>
<strong>File:</strong> src/components/pages/Alerts.tsx  
- Download functionality with icons  
- Small size variant with borders

<h3>2. Login Form</h3>
<strong>File:</strong> src/components/features/login/LoginForm.tsx  
- Form submission with loading states  
- Full width styling with custom colors  
- Disabled state handling

<h3>3. Page Not Found</h3>
<strong>File:</strong> src/components/pages/PageNotFound.tsx  
- Navigation buttons with different variants  
- Ghost and outline variants  
- Icon + text combinations

<h3>4. Filter Button</h3>
<strong>File:</strong> src/components/common/filter-button/FilterButton.tsx  
- Secondary variant with custom styling  
- Small size with icons

<h3>5. Email Dialog</h3>
<strong>File:</strong> src/components/common/ui/EmailDialog.tsx  
- Dialog actions (Cancel/Send)  
- Outline variant for secondary actions

<h3>6. Date Picker</h3>
<strong>File:</strong> src/components/common/ui/DatePicker.tsx  
- AsChild prop usage with Popover  
- Outline variant for form controls

<h2>Component Features</h2>
<ul>
<li><strong>Variants:</strong> default, destructive, outline, secondary, ghost, link</li>
<li><strong>Sizes:</strong> default, sm, lg, icon</li>
<li><strong>AsChild:</strong> Renders as child component using Radix Slot</li>
<li><strong>Accessibility:</strong> Focus management and ARIA support</li>
<li><strong>Icons:</strong> Automatic icon sizing and spacing</li>
<li><strong>Loading states:</strong> Disabled state with opacity</li>
</ul>

<h2>Design System Integration</h2>
- Built with CVA for consistent styling  
- Works with theming system and design tokens  
- Fully responsive and keyboard-accessible  
</div>
                `,
            },
        },
    },
    tags: ['autodocs'],
    argTypes: {
        variant: {
            control: 'select',
            options: ['default', 'destructive', 'outline', 'secondary', 'ghost', 'link'],
            description: 'Button visual variant',
        },
        size: {
            control: 'select',
            options: ['default', 'sm', 'lg', 'icon'],
            description: 'Button size',
        },
        asChild: {
            control: 'boolean',
            description: 'Render as child component',
        },
        disabled: {
            control: 'boolean',
            description: 'Disabled state',
        },
        'aria-label': {
            control: 'text',
            description: 'Accessibility label for screen readers',
        },
        'aria-describedby': {
            control: 'text',
            description: 'ID of element that describes the button',
        },
    },
    args: { onClick: fn() },
} satisfies Meta<typeof Button>;

export default meta;
type Story = StoryObj<typeof meta>;

// ============================================================================
// BASIC VARIANTS
// ============================================================================

export const Default: Story = {
    args: {
        children: 'Default Button',
        variant: 'default',
    },
};

export const Destructive: Story = {
    args: {
        variant: 'destructive',
        children: 'Delete',
    },
};

export const Outline: Story = {
    args: {
        variant: 'outline',
        children: 'Outline Button',
    },
};

export const Secondary: Story = {
    args: {
        variant: 'secondary',
        children: 'Secondary',
    },
};

export const Ghost: Story = {
    args: {
        variant: 'ghost',
        children: 'Ghost Button',
    },
};

export const Link: Story = {
    args: {
        variant: 'link',
        children: 'Link Button',
    },
};

// ============================================================================
// SIZE VARIANTS
// ============================================================================

export const Small: Story = {
    args: {
        size: 'sm',
        children: 'Small Button',
    },
};

export const Large: Story = {
    args: {
        size: 'lg',
        children: 'Large Button',
    },
};

export const IconButton: Story = {
    args: {
        size: 'icon',
        children: <DownloadIcon />,
        'aria-label': 'Download file',
    },
};

// ============================================================================
// ICON EXAMPLES
// ============================================================================

export const WithIcon: Story = {
    args: {
        children: (
            <>
                <DownloadIcon />
                Download
            </>
        ),
    },
};

export const IconOnly: Story = {
    args: {
        variant: 'outline',
        size: 'icon',
        children: <FilterIcon />,
        'aria-label': 'Filter options',
    },
};

export const IconRight: Story = {
    args: {
        children: (
            <>
                Download
                <DownloadIcon />
            </>
        ),
    },
};

// ============================================================================
// STATES
// ============================================================================

export const Disabled: Story = {
    args: {
        disabled: true,
        children: 'Disabled Button',
    },
};

export const DisabledWithIcon: Story = {
    args: {
        disabled: true,
        children: (
            <>
                <LoaderIcon />
                Loading...
            </>
        ),
    },
};

export const LoadingState: Story = {
    args: {
        disabled: true,
        'aria-busy': 'true',
        'aria-live': 'polite',
        children: (
            <>
                <LoaderIcon />
                Processing...
            </>
        ),
    },
};

export const ErrorState: Story = {
    args: {
        variant: 'destructive',
        'aria-invalid': 'true',
        'aria-describedby': 'error-message',
        children: (
            <>
                <ErrorIcon />
                Error occurred
            </>
        ),
    },
    render: (args) => (
        <div className="space-y-2">
            <Button {...args} />
            <div id="error-message" className="text-sm text-red-600">
                Please try again later
            </div>
        </div>
    ),
};

// ============================================================================
// ACCESSIBILITY EXAMPLES
// ============================================================================

export const WithAriaLabel: Story = {
    args: {
        children: <DownloadIcon />,
        'aria-label': 'Download trip data as Excel file',
        size: 'icon',
    },
};

export const WithAriaDescribedby: Story = {
    args: {
        children: 'Submit Form',
        'aria-describedby': 'submit-help',
    },
    render: (args) => (
        <div className="space-y-2">
            <Button {...args} />
            <div id="submit-help" className="text-sm text-gray-600">
                This will save your changes and send notifications
            </div>
        </div>
    ),
};

export const KeyboardNavigation: Story = {
    render: () => (
        <div className="space-x-2">
            <Button tabIndex={0}>First Button</Button>
            <Button tabIndex={0}>Second Button</Button>
            <Button tabIndex={0}>Third Button</Button>
        </div>
    ),
    parameters: {
        docs: {
            description: {
                story: 'Demonstrates keyboard navigation between buttons. Use Tab to navigate and Enter/Space to activate.',
            },
        },
    },
};

// ============================================================================
// ASCHILD EXAMPLES
// ============================================================================

export const AsChildLink: Story = {
    args: {
        asChild: true,
        children: <a href="#example">Link Button</a>,
    },
    parameters: {
        docs: {
            description: {
                story: 'Button rendered as an anchor tag using the asChild prop. Useful for navigation buttons.',
            },
        },
    },
};

export const AsChildWithRole: Story = {
    args: {
        asChild: true,
        children: (
            <div role="button" tabIndex={0}>
                Div as Button
            </div>
        ),
    },
    parameters: {
        docs: {
            description: {
                story: 'Button rendered as a div with proper ARIA role and keyboard support.',
            },
        },
    },
};

// ============================================================================
// EDGE CASES
// ============================================================================

export const LongText: Story = {
    args: {
        children: 'This is a button with very long text that might wrap to multiple lines',
        className: 'max-w-xs',
    },
};

export const MultipleIcons: Story = {
    args: {
        children: (
            <>
                <DownloadIcon />
                Download
                <FilterIcon />
            </>
        ),
    },
};

export const EmptyButton: Story = {
    args: {
        children: '',
        'aria-label': 'Empty button for testing',
    },
};

// ============================================================================
// REAL USAGE EXAMPLES
// ============================================================================

export const AlertsPageDownload: Story = {
    name: 'Real Usage: Alerts Download',
    args: {
        size: 'sm',
        variant: 'default',
        className: 'border',
        children: (
            <>
                <DownloadIcon />
                Download Trip Panel as XLS
            </>
        ),
    },
    parameters: {
        docs: {
            description: {
                story: 'This example shows how the Button is used in the Alerts page (`src/components/pages/Alerts.tsx`) for downloading data with a small size and border styling.',
            },
        },
    },
};

export const FilterButtonExample: Story = {
    name: 'Real Usage: Filter Button',
    args: {
        size: 'sm',
        variant: 'secondary',
        className: 'border border-blue-600 text-blue-600',
        children: (
            <>
                <FilterIcon />
                Filter
            </>
        ),
    },
    parameters: {
        docs: {
            description: {
                story: 'This example replicates the Button usage in FilterButton component (`src/components/common/filter-button/FilterButton.tsx`) with custom blue styling.',
            },
        },
    },
};

export const LoginFormExample: Story = {
    name: 'Real Usage: Login Form',
    args: {
        type: 'submit',
        variant: 'outline',
        className: 'bg-[var(--blue-color)] text-[#f2f2f2] w-full mt-5',
        children: (
            <>
                <LoaderIcon />
                Login
            </>
        ),
    },
    parameters: {
        docs: {
            description: {
                story: 'This example shows the Button usage in LoginForm (`src/components/features/login/LoginForm.tsx`) with custom CSS variables and full width styling.',
            },
        },
    },
};

export const PageNotFoundNavigation: Story = {
    name: 'Real Usage: Page Not Found Navigation',
    render: () => (
        <div className="flex gap-4">
            <Button variant="ghost" className="w-40 border border-[#777] hover:text-[var(--blue-color)]" onClick={fn()}>
                <ArrowLeftIcon />
                Back
            </Button>
            <Button variant="outline" className="w-40 hover:text-[var(--blue-color)]" onClick={fn()}>
                <StatsIcon />
                Dashboard
            </Button>
        </div>
    ),
    parameters: {
        docs: {
            description: {
                story: 'This example shows how Buttons are used in PageNotFound component (`src/components/pages/PageNotFound.tsx`) for navigation with different variants and custom hover colors.',
            },
        },
    },
};

export const EmailDialogActions: Story = {
    name: 'Real Usage: Email Dialog Actions',
    render: () => (
        <div className="flex gap-2">
            <Button type="button" variant="outline" onClick={fn()}>
                Cancel
            </Button>
            <Button type="submit" onClick={fn()}>
                Send
            </Button>
        </div>
    ),
    parameters: {
        docs: {
            description: {
                story: 'This example shows Button usage in EmailDialog (`src/components/common/ui/EmailDialog.tsx`) for dialog actions with different variants.',
            },
        },
    },
};

export const DatePickerTrigger: Story = {
    name: 'Real Usage: Date Picker Trigger',
    args: {
        variant: 'outline',
        className: 'w-full justify-start text-left font-normal text-muted-foreground',
        children: (
            <>
                <CalendarIcon />
                Pick a date
            </>
        ),
    },
    parameters: {
        docs: {
            description: {
                story: 'This example shows Button usage in DatePicker (`src/components/common/ui/DatePicker.tsx`) as a trigger with outline variant and left-aligned text.',
            },
        },
    },
};

// ============================================================================
// PERFORMANCE EXAMPLES
// ============================================================================

export const ManyButtons: Story = {
    render: () => (
        <div className="grid grid-cols-4 gap-2 max-w-md">
            {Array.from({ length: 20 }, (_, i) => (
                <Button key={i} size="sm" variant={i % 2 === 0 ? 'default' : 'outline'}>
                    Button {i + 1}
                </Button>
            ))}
        </div>
    ),
    parameters: {
        docs: {
            description: {
                story: 'Performance test with many buttons. Useful for testing rendering performance and memory usage.',
            },
        },
    },
};
