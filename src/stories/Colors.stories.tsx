/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint-disable no-console */
import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';

const meta: Meta = {
    title: 'Design System/Colors',
    parameters: {
        layout: 'fullscreen',
        docs: {
            description: {
                component: `
# Complete Color System

This document showcases the complete color system used throughout the application. Each color includes:

- **Color Preview**: Visual representation of the color
- **CSS Variable**: The CSS custom property name for use in stylesheets
- **Tailwind Class**: The corresponding Tailwind CSS class name
- **Color Values**: Both hexadecimal and RGBA representations

## Color Categories

- **Primary Colors**: Green-based colors used for main actions, links, and primary UI elements
- **Secondary Colors**: Cyan/blue-based colors used for secondary actions and accents
- **Neutral Colors**: Grayscale colors used for text, backgrounds, and borders
- **Semantic Colors**: Error, success, and warning colors for status indicators

## Usage Guidelines

- Use primary colors for main CTAs, links, and important UI elements
- Use secondary colors for secondary actions and subtle accents
- Use neutral colors for text, backgrounds, and borders
- Use semantic colors only for their intended purposes (error states, success messages, warnings)

## Implementation

Colors are defined as CSS custom properties in \`tokens.css\` and can be used in three ways:

1. **CSS Variables**: \`var(--color-primary-500)\`
2. **Tailwind Classes**: \`bg-primary-500\`, \`text-primary-500\`
3. **Direct Values**: \`#29be7a\` or \`rgba(41, 190, 122, 1)\`
                `,
            },
        },
    },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Helper function to convert rgba to hex
const rgbaToHex = (rgba: string): string => {
    const match = rgba.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([\d.]+))?\)/);
    if (!match) return rgba;

    const r = parseInt(match[1]);
    const g = parseInt(match[2]);
    const b = parseInt(match[3]);

    return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
};

// Comprehensive color swatch component
const ComprehensiveColorSwatch = ({
    name,
    cssVariable,
    tailwindClass,
    color,
    description,
}: {
    name: string;
    cssVariable: string;
    tailwindClass: string;
    color: string;
    description?: string;
}) => {
    const [copied, setCopied] = useState(false);
    const hexValue = rgbaToHex(color);

    const copyToClipboard = async (text: string) => {
        try {
            await navigator.clipboard.writeText(text);
            setCopied(true);
            setTimeout(() => setCopied(false), 2000);
        } catch (err) {
            console.error('Failed to copy: ', err);
        }
    };

    return (
        <div className="flex flex-col gap-3 p-4 border rounded-lg bg-white hover:shadow-md transition-shadow">
            <div className="flex items-center gap-3">
                <div
                    className="w-16 h-16 rounded-lg border border-gray-200 shadow-sm cursor-pointer"
                    style={{ backgroundColor: color }}
                    onClick={() => copyToClipboard(color)}
                    title="Click to copy color value"
                />
                <div className="flex-1">
                    <h3 className="font-semibold text-sm">{name}</h3>
                    <p className="text-xs text-gray-600 font-mono">{cssVariable}</p>
                </div>
            </div>

            <div className="space-y-2">
                <div className="flex justify-between items-center text-xs">
                    <span className="text-gray-500">Tailwind:</span>
                    <code
                        className="bg-gray-100 px-2 py-1 rounded font-mono cursor-pointer hover:bg-gray-200"
                        onClick={() => copyToClipboard(tailwindClass)}>
                        {tailwindClass}
                    </code>
                </div>

                <div className="flex justify-between items-center text-xs">
                    <span className="text-gray-500">Hex:</span>
                    <code
                        className="bg-gray-100 px-2 py-1 rounded font-mono cursor-pointer hover:bg-gray-200"
                        onClick={() => copyToClipboard(hexValue)}>
                        {hexValue}
                    </code>
                </div>

                <div className="flex justify-between items-center text-xs">
                    <span className="text-gray-500">RGBA:</span>
                    <code
                        className="bg-gray-100 px-2 py-1 rounded font-mono cursor-pointer hover:bg-gray-200"
                        onClick={() => copyToClipboard(color)}>
                        {color}
                    </code>
                </div>
            </div>

            {description && <p className="text-xs text-gray-500">{description}</p>}

            <div className="flex gap-2">
                <button
                    onClick={() => copyToClipboard(cssVariable)}
                    className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded transition-colors">
                    {copied ? 'Copied!' : 'Copy CSS Variable'}
                </button>
                <button
                    onClick={() => copyToClipboard(tailwindClass)}
                    className="text-xs px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded transition-colors">
                    Copy Tailwind Class
                </button>
            </div>
        </div>
    );
};

// Color palette with comprehensive information
const ComprehensiveColorPalette = ({
    title,
    colors,
    description,
}: {
    title: string;
    colors: Array<{ name: string; cssVariable: string; tailwindClass: string; color: string; description?: string }>;
    description?: string;
}) => (
    <div className="mb-8">
        <h2 className="text-xl font-bold mb-4">{title}</h2>
        {description && <p className="text-gray-600 mb-4">{description}</p>}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {colors.map((color) => (
                <ComprehensiveColorSwatch key={color.name} {...color} />
            ))}
        </div>
    </div>
);

export const PrimaryColors: Story = {
    render: () => (
        <div className="p-8 bg-gray-50 min-h-screen">
            <div className="max-w-7xl mx-auto">
                <h1 className="text-3xl font-bold mb-8">Primary Colors</h1>

                <ComprehensiveColorPalette
                    title="Primary Colors"
                    description="Green-based colors used for main actions, links, and primary UI elements"
                    colors={[
                        {
                            name: 'Primary 50',
                            cssVariable: 'var(--color-primary-50)',
                            tailwindClass: 'bg-primary-50',
                            color: 'rgba(241, 252, 246, 1)',
                            description: 'Lightest primary color',
                        },
                        {
                            name: 'Primary 100',
                            cssVariable: 'var(--color-primary-100)',
                            tailwindClass: 'bg-primary-100',
                            color: 'rgba(221, 251, 236, 1)',
                            description: 'Very light primary',
                        },
                        {
                            name: 'Primary 200',
                            cssVariable: 'var(--color-primary-200)',
                            tailwindClass: 'bg-primary-200',
                            color: 'rgba(190, 244, 218, 1)',
                            description: 'Light primary',
                        },
                        {
                            name: 'Primary 300',
                            cssVariable: 'var(--color-primary-300)',
                            tailwindClass: 'bg-primary-300',
                            color: 'rgba(138, 235, 190, 1)',
                            description: 'Medium light primary',
                        },
                        {
                            name: 'Primary 400',
                            cssVariable: 'var(--color-primary-400)',
                            tailwindClass: 'bg-primary-400',
                            color: 'rgba(80, 216, 154, 1)',
                            description: 'Medium primary',
                        },
                        {
                            name: 'Primary 500',
                            cssVariable: 'var(--color-primary-500)',
                            tailwindClass: 'bg-primary-500',
                            color: 'rgba(41, 190, 122, 1)',
                            description: 'Base primary color',
                        },
                        {
                            name: 'Primary 600',
                            cssVariable: 'var(--color-primary-600)',
                            tailwindClass: 'bg-primary-600',
                            color: 'rgba(28, 157, 98, 1)',
                            description: 'Medium dark primary',
                        },
                        {
                            name: 'Primary 700',
                            cssVariable: 'var(--color-primary-700)',
                            tailwindClass: 'bg-primary-700',
                            color: 'rgba(27, 131, 84, 1)',
                            description: 'Dark primary',
                        },
                        {
                            name: 'Primary 800',
                            cssVariable: 'var(--color-primary-800)',
                            tailwindClass: 'bg-primary-800',
                            color: 'rgba(25, 98, 66, 1)',
                            description: 'Very dark primary',
                        },
                        {
                            name: 'Primary 900',
                            cssVariable: 'var(--color-primary-900)',
                            tailwindClass: 'bg-primary-900',
                            color: 'rgba(23, 80, 56, 1)',
                            description: 'Darkest primary',
                        },
                        {
                            name: 'Primary 950',
                            cssVariable: 'var(--color-primary-950)',
                            tailwindClass: 'bg-primary-950',
                            color: 'rgba(7, 44, 28, 1)',
                            description: 'Deepest primary',
                        },
                    ]}
                />
            </div>
        </div>
    ),
};

export const SecondaryColors: Story = {
    render: () => (
        <div className="p-8 bg-gray-50 min-h-screen">
            <div className="max-w-7xl mx-auto">
                <h1 className="text-3xl font-bold mb-8">Secondary Colors</h1>

                <ComprehensiveColorPalette
                    title="Secondary Colors"
                    description="Cyan/blue-based colors used for secondary actions and accents"
                    colors={[
                        {
                            name: 'Secondary 50',
                            cssVariable: 'var(--color-secondary-50)',
                            tailwindClass: 'bg-secondary-50',
                            color: 'rgba(238, 252, 253, 1)',
                            description: 'Lightest secondary color',
                        },
                        {
                            name: 'Secondary 100',
                            cssVariable: 'var(--color-secondary-100)',
                            tailwindClass: 'bg-secondary-100',
                            color: 'rgba(211, 243, 250, 1)',
                            description: 'Very light secondary',
                        },
                        {
                            name: 'Secondary 200',
                            cssVariable: 'var(--color-secondary-200)',
                            tailwindClass: 'bg-secondary-200',
                            color: 'rgba(172, 231, 245, 1)',
                            description: 'Light secondary',
                        },
                        {
                            name: 'Secondary 300',
                            cssVariable: 'var(--color-secondary-300)',
                            tailwindClass: 'bg-secondary-300',
                            color: 'rgba(115, 211, 237, 1)',
                            description: 'Medium light secondary',
                        },
                        {
                            name: 'Secondary 400',
                            cssVariable: 'var(--color-secondary-400)',
                            tailwindClass: 'bg-secondary-400',
                            color: 'rgba(50, 183, 222, 1)',
                            description: 'Medium secondary',
                        },
                        {
                            name: 'Secondary 500',
                            cssVariable: 'var(--color-secondary-500)',
                            tailwindClass: 'bg-secondary-500',
                            color: 'rgba(23, 159, 202, 1)',
                            description: 'Base secondary color',
                        },
                        {
                            name: 'Secondary 600',
                            cssVariable: 'var(--color-secondary-600)',
                            tailwindClass: 'bg-secondary-600',
                            color: 'rgba(21, 123, 165, 1)',
                            description: 'Medium dark secondary',
                        },
                        {
                            name: 'Secondary 700',
                            cssVariable: 'var(--color-secondary-700)',
                            tailwindClass: 'bg-secondary-700',
                            color: 'rgba(24, 100, 134, 1)',
                            description: 'Dark secondary',
                        },
                        {
                            name: 'Secondary 800',
                            cssVariable: 'var(--color-secondary-800)',
                            tailwindClass: 'bg-secondary-800',
                            color: 'rgba(29, 82, 109, 1)',
                            description: 'Very dark secondary',
                        },
                        {
                            name: 'Secondary 900',
                            cssVariable: 'var(--color-secondary-900)',
                            tailwindClass: 'bg-secondary-900',
                            color: 'rgba(28, 69, 93, 1)',
                            description: 'Darkest secondary',
                        },
                        {
                            name: 'Secondary 950',
                            cssVariable: 'var(--color-secondary-950)',
                            tailwindClass: 'bg-secondary-950',
                            color: 'rgba(13, 44, 63, 1)',
                            description: 'Deepest secondary',
                        },
                    ]}
                />
            </div>
        </div>
    ),
};

export const NeutralColors: Story = {
    render: () => (
        <div className="p-8 bg-gray-50 min-h-screen">
            <div className="max-w-7xl mx-auto">
                <h1 className="text-3xl font-bold mb-8">Neutral Colors</h1>

                <ComprehensiveColorPalette
                    title="Neutral Colors"
                    description="Grayscale colors used for text, backgrounds, and borders"
                    colors={[
                        {
                            name: 'Neutral 50',
                            cssVariable: 'var(--color-neutral-50)',
                            tailwindClass: 'bg-neutral-50',
                            color: 'rgba(255, 255, 255, 1)',
                            description: 'Pure white',
                        },
                        {
                            name: 'Neutral 100',
                            cssVariable: 'var(--color-neutral-100)',
                            tailwindClass: 'bg-neutral-100',
                            color: 'rgba(239, 239, 239, 1)',
                            description: 'Very light gray',
                        },
                        {
                            name: 'Neutral 200',
                            cssVariable: 'var(--color-neutral-200)',
                            tailwindClass: 'bg-neutral-200',
                            color: 'rgba(220, 220, 220, 1)',
                            description: 'Light gray',
                        },
                        {
                            name: 'Neutral 300',
                            cssVariable: 'var(--color-neutral-300)',
                            tailwindClass: 'bg-neutral-300',
                            color: 'rgba(189, 189, 189, 1)',
                            description: 'Medium light gray',
                        },
                        {
                            name: 'Neutral 400',
                            cssVariable: 'var(--color-neutral-400)',
                            tailwindClass: 'bg-neutral-400',
                            color: 'rgba(152, 152, 152, 1)',
                            description: 'Medium gray',
                        },
                        {
                            name: 'Neutral 500',
                            cssVariable: 'var(--color-neutral-500)',
                            tailwindClass: 'bg-neutral-500',
                            color: 'rgba(124, 124, 124, 1)',
                            description: 'Base gray',
                        },
                        {
                            name: 'Neutral 600',
                            cssVariable: 'var(--color-neutral-600)',
                            tailwindClass: 'bg-neutral-600',
                            color: 'rgba(101, 101, 101, 1)',
                            description: 'Medium dark gray',
                        },
                        {
                            name: 'Neutral 700',
                            cssVariable: 'var(--color-neutral-700)',
                            tailwindClass: 'bg-neutral-700',
                            color: 'rgba(82, 82, 82, 1)',
                            description: 'Dark gray',
                        },
                        {
                            name: 'Neutral 800',
                            cssVariable: 'var(--color-neutral-800)',
                            tailwindClass: 'bg-neutral-800',
                            color: 'rgba(70, 70, 70, 1)',
                            description: 'Very dark gray',
                        },
                        {
                            name: 'Neutral 900',
                            cssVariable: 'var(--color-neutral-900)',
                            tailwindClass: 'bg-neutral-900',
                            color: 'rgba(61, 61, 61, 1)',
                            description: 'Darkest gray',
                        },
                        {
                            name: 'Neutral 950',
                            cssVariable: 'var(--color-neutral-950)',
                            tailwindClass: 'bg-neutral-950',
                            color: 'rgba(41, 41, 41, 1)',
                            description: 'Deepest gray',
                        },
                    ]}
                />
            </div>
        </div>
    ),
};

export const SemanticColors: Story = {
    render: () => (
        <div className="p-8 bg-gray-50 min-h-screen">
            <div className="max-w-7xl mx-auto">
                <h1 className="text-3xl font-bold mb-8">Semantic Colors</h1>

                <ComprehensiveColorPalette
                    title="Semantic Colors"
                    description="Colors used for specific purposes and status indicators"
                    colors={[
                        {
                            name: 'Error 500',
                            cssVariable: 'var(--color-error-500)',
                            tailwindClass: 'bg-error-500',
                            color: 'rgba(228, 81, 79, 1)',
                            description: 'Error states and validation messages',
                        },
                        {
                            name: 'Success 500',
                            cssVariable: 'var(--color-success-500)',
                            tailwindClass: 'bg-success-500',
                            color: 'rgba(78, 153, 102, 1)',
                            description: 'Success states and positive feedback',
                        },
                        {
                            name: 'Warning 500',
                            cssVariable: 'var(--color-warning-500)',
                            tailwindClass: 'bg-warning-500',
                            color: 'rgba(249, 120, 22, 1)',
                            description: 'Warning states and caution messages',
                        },
                    ]}
                />
            </div>
        </div>
    ),
};

export const AllColors: Story = {
    render: () => (
        <div className="p-8 bg-gray-50 min-h-screen">
            <div className="max-w-7xl mx-auto">
                <h1 className="text-3xl font-bold mb-8">Complete Color System</h1>

                <ComprehensiveColorPalette
                    title="Primary Colors"
                    description="Green-based colors used for main actions, links, and primary UI elements"
                    colors={[
                        {
                            name: 'Primary 50',
                            cssVariable: 'var(--color-primary-50)',
                            tailwindClass: 'bg-primary-50',
                            color: 'rgba(241, 252, 246, 1)',
                        },
                        {
                            name: 'Primary 100',
                            cssVariable: 'var(--color-primary-100)',
                            tailwindClass: 'bg-primary-100',
                            color: 'rgba(221, 251, 236, 1)',
                        },
                        {
                            name: 'Primary 200',
                            cssVariable: 'var(--color-primary-200)',
                            tailwindClass: 'bg-primary-200',
                            color: 'rgba(190, 244, 218, 1)',
                        },
                        {
                            name: 'Primary 300',
                            cssVariable: 'var(--color-primary-300)',
                            tailwindClass: 'bg-primary-300',
                            color: 'rgba(138, 235, 190, 1)',
                        },
                        {
                            name: 'Primary 400',
                            cssVariable: 'var(--color-primary-400)',
                            tailwindClass: 'bg-primary-400',
                            color: 'rgba(80, 216, 154, 1)',
                        },
                        {
                            name: 'Primary 500',
                            cssVariable: 'var(--color-primary-500)',
                            tailwindClass: 'bg-primary-500',
                            color: 'rgba(41, 190, 122, 1)',
                        },
                        {
                            name: 'Primary 600',
                            cssVariable: 'var(--color-primary-600)',
                            tailwindClass: 'bg-primary-600',
                            color: 'rgba(28, 157, 98, 1)',
                        },
                        {
                            name: 'Primary 700',
                            cssVariable: 'var(--color-primary-700)',
                            tailwindClass: 'bg-primary-700',
                            color: 'rgba(27, 131, 84, 1)',
                        },
                        {
                            name: 'Primary 800',
                            cssVariable: 'var(--color-primary-800)',
                            tailwindClass: 'bg-primary-800',
                            color: 'rgba(25, 98, 66, 1)',
                        },
                        {
                            name: 'Primary 900',
                            cssVariable: 'var(--color-primary-900)',
                            tailwindClass: 'bg-primary-900',
                            color: 'rgba(23, 80, 56, 1)',
                        },
                        {
                            name: 'Primary 950',
                            cssVariable: 'var(--color-primary-950)',
                            tailwindClass: 'bg-primary-950',
                            color: 'rgba(7, 44, 28, 1)',
                        },
                    ]}
                />

                <ComprehensiveColorPalette
                    title="Secondary Colors"
                    description="Cyan/blue-based colors used for secondary actions and accents"
                    colors={[
                        {
                            name: 'Secondary 50',
                            cssVariable: 'var(--color-secondary-50)',
                            tailwindClass: 'bg-secondary-50',
                            color: 'rgba(238, 252, 253, 1)',
                        },
                        {
                            name: 'Secondary 100',
                            cssVariable: 'var(--color-secondary-100)',
                            tailwindClass: 'bg-secondary-100',
                            color: 'rgba(211, 243, 250, 1)',
                        },
                        {
                            name: 'Secondary 200',
                            cssVariable: 'var(--color-secondary-200)',
                            tailwindClass: 'bg-secondary-200',
                            color: 'rgba(172, 231, 245, 1)',
                        },
                        {
                            name: 'Secondary 300',
                            cssVariable: 'var(--color-secondary-300)',
                            tailwindClass: 'bg-secondary-300',
                            color: 'rgba(115, 211, 237, 1)',
                        },
                        {
                            name: 'Secondary 400',
                            cssVariable: 'var(--color-secondary-400)',
                            tailwindClass: 'bg-secondary-400',
                            color: 'rgba(50, 183, 222, 1)',
                        },
                        {
                            name: 'Secondary 500',
                            cssVariable: 'var(--color-secondary-500)',
                            tailwindClass: 'bg-secondary-500',
                            color: 'rgba(23, 159, 202, 1)',
                        },
                        {
                            name: 'Secondary 600',
                            cssVariable: 'var(--color-secondary-600)',
                            tailwindClass: 'bg-secondary-600',
                            color: 'rgba(21, 123, 165, 1)',
                        },
                        {
                            name: 'Secondary 700',
                            cssVariable: 'var(--color-secondary-700)',
                            tailwindClass: 'bg-secondary-700',
                            color: 'rgba(24, 100, 134, 1)',
                        },
                        {
                            name: 'Secondary 800',
                            cssVariable: 'var(--color-secondary-800)',
                            tailwindClass: 'bg-secondary-800',
                            color: 'rgba(29, 82, 109, 1)',
                        },
                        {
                            name: 'Secondary 900',
                            cssVariable: 'var(--color-secondary-900)',
                            tailwindClass: 'bg-secondary-900',
                            color: 'rgba(28, 69, 93, 1)',
                        },
                        {
                            name: 'Secondary 950',
                            cssVariable: 'var(--color-secondary-950)',
                            tailwindClass: 'bg-secondary-950',
                            color: 'rgba(13, 44, 63, 1)',
                        },
                    ]}
                />

                <ComprehensiveColorPalette
                    title="Neutral Colors"
                    description="Grayscale colors used for text, backgrounds, and borders"
                    colors={[
                        {
                            name: 'Neutral 50',
                            cssVariable: 'var(--color-neutral-50)',
                            tailwindClass: 'bg-neutral-50',
                            color: 'rgba(255, 255, 255, 1)',
                        },
                        {
                            name: 'Neutral 100',
                            cssVariable: 'var(--color-neutral-100)',
                            tailwindClass: 'bg-neutral-100',
                            color: 'rgba(239, 239, 239, 1)',
                        },
                        {
                            name: 'Neutral 200',
                            cssVariable: 'var(--color-neutral-200)',
                            tailwindClass: 'bg-neutral-200',
                            color: 'rgba(220, 220, 220, 1)',
                        },
                        {
                            name: 'Neutral 300',
                            cssVariable: 'var(--color-neutral-300)',
                            tailwindClass: 'bg-neutral-300',
                            color: 'rgba(189, 189, 189, 1)',
                        },
                        {
                            name: 'Neutral 400',
                            cssVariable: 'var(--color-neutral-400)',
                            tailwindClass: 'bg-neutral-400',
                            color: 'rgba(152, 152, 152, 1)',
                        },
                        {
                            name: 'Neutral 500',
                            cssVariable: 'var(--color-neutral-500)',
                            tailwindClass: 'bg-neutral-500',
                            color: 'rgba(124, 124, 124, 1)',
                        },
                        {
                            name: 'Neutral 600',
                            cssVariable: 'var(--color-neutral-600)',
                            tailwindClass: 'bg-neutral-600',
                            color: 'rgba(101, 101, 101, 1)',
                        },
                        {
                            name: 'Neutral 700',
                            cssVariable: 'var(--color-neutral-700)',
                            tailwindClass: 'bg-neutral-700',
                            color: 'rgba(82, 82, 82, 1)',
                        },
                        {
                            name: 'Neutral 800',
                            cssVariable: 'var(--color-neutral-800)',
                            tailwindClass: 'bg-neutral-800',
                            color: 'rgba(70, 70, 70, 1)',
                        },
                        {
                            name: 'Neutral 900',
                            cssVariable: 'var(--color-neutral-900)',
                            tailwindClass: 'bg-neutral-900',
                            color: 'rgba(61, 61, 61, 1)',
                        },
                        {
                            name: 'Neutral 950',
                            cssVariable: 'var(--color-neutral-950)',
                            tailwindClass: 'bg-neutral-950',
                            color: 'rgba(41, 41, 41, 1)',
                        },
                    ]}
                />

                <ComprehensiveColorPalette
                    title="Semantic Colors"
                    description="Colors used for specific purposes and status indicators"
                    colors={[
                        {
                            name: 'Error 500',
                            cssVariable: 'var(--color-error-500)',
                            tailwindClass: 'bg-error-500',
                            color: 'rgba(228, 81, 79, 1)',
                        },
                        {
                            name: 'Success 500',
                            cssVariable: 'var(--color-success-500)',
                            tailwindClass: 'bg-success-500',
                            color: 'rgba(78, 153, 102, 1)',
                        },
                        {
                            name: 'Warning 500',
                            cssVariable: 'var(--color-warning-500)',
                            tailwindClass: 'bg-warning-500',
                            color: 'rgba(249, 120, 22, 1)',
                        },
                    ]}
                />
            </div>
        </div>
    ),
};

export const ColorUsageExamples: Story = {
    render: () => (
        <div className="p-8 bg-gray-50 min-h-screen">
            <div className="max-w-7xl mx-auto">
                <h1 className="text-3xl font-bold mb-8">Color Usage Examples</h1>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Primary Usage */}
                    <div className="bg-white p-6 rounded-lg shadow-sm">
                        <h2 className="text-xl font-semibold mb-4 text-green-700">Primary Color Usage</h2>
                        <div className="space-y-4">
                            <button className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors">
                                Primary Button
                            </button>
                            <a href="#" className="text-green-600 hover:text-green-700 underline">
                                Primary Link
                            </a>
                            <div className="p-3 bg-green-50 border border-green-200 rounded">
                                <p className="text-green-800">Primary background with text</p>
                            </div>
                        </div>
                    </div>

                    {/* Secondary Usage */}
                    <div className="bg-white p-6 rounded-lg shadow-sm">
                        <h2 className="text-xl font-semibold mb-4 text-cyan-700">Secondary Color Usage</h2>
                        <div className="space-y-4">
                            <button className="px-4 py-2 bg-cyan-500 text-white rounded hover:bg-cyan-600 transition-colors">
                                Secondary Button
                            </button>
                            <a href="#" className="text-cyan-600 hover:text-cyan-700 underline">
                                Secondary Link
                            </a>
                            <div className="p-3 bg-cyan-50 border border-cyan-200 rounded">
                                <p className="text-cyan-800">Secondary background with text</p>
                            </div>
                        </div>
                    </div>

                    {/* Semantic Usage */}
                    <div className="bg-white p-6 rounded-lg shadow-sm">
                        <h2 className="text-xl font-semibold mb-4">Semantic Color Usage</h2>
                        <div className="space-y-4">
                            <div className="p-3 bg-red-50 border border-red-200 rounded">
                                <p className="text-red-800">Error message example</p>
                            </div>
                            <div className="p-3 bg-green-50 border border-green-200 rounded">
                                <p className="text-green-800">Success message example</p>
                            </div>
                            <div className="p-3 bg-orange-50 border border-orange-200 rounded">
                                <p className="text-orange-800">Warning message example</p>
                            </div>
                        </div>
                    </div>

                    {/* Neutral Usage */}
                    <div className="bg-white p-6 rounded-lg shadow-sm">
                        <h2 className="text-xl font-semibold mb-4">Neutral Color Usage</h2>
                        <div className="space-y-4">
                            <p className="text-gray-900">Primary text color</p>
                            <p className="text-gray-600">Secondary text color</p>
                            <p className="text-gray-400">Disabled text color</p>
                            <div className="p-3 bg-gray-100 border border-gray-200 rounded">
                                <p className="text-gray-700">Neutral background with text</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    ),
};
