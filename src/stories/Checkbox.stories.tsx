import { useState } from 'react';
import type { Meta, StoryObj } from '@storybook/react';

import { Checkbox } from '@/components/common/ui/Checkbox';

/**
 * Storybook metadata configuration for Checkbox component.
 */
const meta: Meta<typeof Checkbox> = {
    title: 'Components/Checkbox',
    component: Checkbox,
    tags: ['autodocs'],
    argTypes: {
        label: {
            description: 'Text label displayed next to the checkbox',
            control: 'text',
        },
        checked: {
            description: 'Controls the checked state (for controlled usage)',
            control: 'boolean',
        },
        disabled: {
            description: 'Disables interaction with the checkbox',
            control: 'boolean',
        },
        isSelectAll: {
            description: 'Special styling for "Select All" functionality',
            control: 'boolean',
        },
        onChange: {
            description: 'Event handler triggered when checked state changes',
            action: 'changed',
        },
    },
};

export default meta;
type Story = StoryObj<typeof Checkbox>;

// ============================================================================
// BASIC EXAMPLES
// ============================================================================

/**
 * Default uncontrolled checkbox example.
 */
export const Default: Story = {
    args: {
        label: 'Accept Terms and Conditions',
    },
};

/**
 * Controlled checkbox example using React local state.
 */
export const Controlled = () => {
    const [checked, setChecked] = useState(false);

    return (
        <Checkbox label="Subscribe to newsletter" checked={checked} onChange={(newChecked) => setChecked(newChecked)} />
    );
};
Controlled.storyName = 'Controlled Checkbox';

/**
 * Disabled checkbox example.
 */
export const Disabled: Story = {
    args: {
        label: 'I am disabled',
        disabled: true,
    },
};

/**
 * Select All checkbox with special styling.
 */
export const SelectAll: Story = {
    args: {
        label: 'Select All Items',
        isSelectAll: true,
    },
};

/**
 * Practical example showing multiple checkboxes with Select All functionality.
 */
export const MultipleSelection = () => {
    const items = ['Option 1', 'Option 2', 'Option 3', 'Option 4'];
    const [selected, setSelected] = useState<string[]>([]);

    const handleSelectAll = (checked: boolean) => {
        setSelected(checked ? items : []);
    };

    const handleItemChange = (item: string, checked: boolean) => {
        setSelected((prev) => (checked ? [...prev, item] : prev.filter((i) => i !== item)));
    };

    const allSelected = selected.length === items.length;

    return (
        <div className="space-y-3">
            <Checkbox label="Select All" isSelectAll={true} checked={allSelected} onChange={handleSelectAll} />
            <div className="border-t pt-3 space-y-2">
                {items.map((item) => (
                    <Checkbox
                        key={item}
                        label={item}
                        checked={selected.includes(item)}
                        onChange={(checked) => handleItemChange(item, checked)}
                    />
                ))}
            </div>
            <div className="text-sm text-gray-600 mt-3">
                Selected: {selected.length} of {items.length} items
            </div>
        </div>
    );
};
MultipleSelection.storyName = 'Multiple Selection with Select All';
