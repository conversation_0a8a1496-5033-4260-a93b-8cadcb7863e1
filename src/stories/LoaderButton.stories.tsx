import { useState } from 'react';
import type { Meta, StoryObj } from '@storybook/react';

import { LoaderButton } from '@/components/common/ui/LoaderButton';

const meta: Meta<typeof LoaderButton> = {
    title: 'Components/LoaderButton',
    component: LoaderButton,
    tags: ['autodocs'],
    argTypes: {
        loading: {
            description: 'Show loading state',
            control: 'boolean',
        },
        disabled: {
            description: 'Disable button',
            control: 'boolean',
        },
        variant: {
            description: 'Button variant',
            control: 'select',
            options: ['default', 'success', 'secondary', 'outline'],
        },
        size: {
            description: 'Button size',
            control: 'select',
            options: ['default', 'sm', 'lg', 'icon'],
        },
    },
};

export default meta;
type Story = StoryObj<typeof LoaderButton>;

export const Default: Story = {
    args: {
        children: 'Click me',
        loading: false,
        isLoadingText: 'Loading...',
        defaultText: 'Click me',
        icon: <span>🚀</span>,
    },
};

export const Loading: Story = {
    args: {
        children: 'Loading...',
        loading: true,
        isLoadingText: 'Loading...',
        defaultText: 'Click me',
        icon: <span>🚀</span>,
    },
};

export const Disabled: Story = {
    args: {
        children: 'Disabled',
        disabled: true,
        loading: false,
        isLoadingText: 'Loading...',
        defaultText: 'Disabled',
        icon: <span>🚀</span>,
    },
};

export const Destructive: Story = {
    args: {
        children: 'Delete',
        variant: 'outline',
        loading: false,
        isLoadingText: 'Deleting...',
        defaultText: 'Delete',
        icon: <span>🗑️</span>,
    },
};

export const Small: Story = {
    args: {
        children: 'Small',
        size: 'sm',
        loading: false,
        isLoadingText: 'Loading...',
        defaultText: 'Small',
        icon: <span>🚀</span>,
    },
};

export const Large: Story = {
    args: {
        children: 'Large',
        size: 'lg',
        loading: false,
        isLoadingText: 'Loading...',
        defaultText: 'Large',
        icon: <span>🚀</span>,
    },
};

export const WithAsyncAction = () => {
    const [loading, setLoading] = useState(false);

    const handleClick = async () => {
        setLoading(true);
        await new Promise((resolve) => setTimeout(resolve, 2000));
        setLoading(false);
    };

    return (
        <LoaderButton
            loading={loading}
            onClick={handleClick}
            icon={<span>🚀</span>}
            isLoadingText="Processing..."
            defaultText="Start Process">
            {loading ? 'Processing...' : 'Start Process'}
        </LoaderButton>
    );
};
WithAsyncAction.storyName = 'With Async Action';
