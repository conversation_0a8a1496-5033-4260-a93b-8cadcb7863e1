import { useState } from 'react';
import type { Meta, StoryObj } from '@storybook/react';

import { Calendar } from '@/components/common/ui/Calendar';

const meta: Meta<typeof Calendar> = {
    title: 'Components/Calendar',
    component: Calendar,
    tags: ['autodocs'],
    argTypes: {
        mode: {
            description: 'Calendar mode',
            control: 'select',
            options: ['single', 'range', 'multiple'],
        },
        selected: {
            description: 'Selected date(s)',
            control: 'object',
        },
        onSelect: {
            description: 'Date selection handler',
            action: 'selected',
        },
    },
};

export default meta;
type Story = StoryObj<typeof Calendar>;

export const Default: Story = {
    args: {},
};

export const WithSelectedDate: Story = {
    args: {
        selected: new Date(),
    },
};

export const RangeMode = () => {
    const [date, setDate] = useState<{ from: Date; to: Date } | undefined>({
        from: new Date(),
        to: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    });

    return (
        <Calendar
            mode="range"
            selected={date}
            onSelect={(selected) => setDate(selected as { from: Date; to: Date } | undefined)}
            className="rounded-md border"
        />
    );
};
RangeMode.storyName = 'Range Selection';

export const MultipleMode = () => {
    const [dates, setDates] = useState<Date[]>([]);

    return (
        <Calendar
            mode="multiple"
            selected={dates}
            onSelect={(selected) => setDates(selected as Date[])}
            required
            className="rounded-md border"
        />
    );
};
MultipleMode.storyName = 'Multiple Selection';
