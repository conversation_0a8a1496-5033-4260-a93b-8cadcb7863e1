import type { Meta, StoryObj } from '@storybook/react';

import Tooltip from '@/components/common/ui/Tooltip';

const meta: Meta<typeof Tooltip> = {
    title: 'Components/Tooltip',
    component: Tooltip,
    tags: ['autodocs'],
    parameters: {
        layout: 'centered',
    },
    argTypes: {
        tooltipMessage: {
            description: 'Translation key for tooltip message',
            control: 'text',
        },
        translationParams: {
            description: 'Translation parameters',
            control: 'object',
        },
    },
};

export default meta;
type Story = StoryObj<typeof Tooltip>;

export const Default: Story = {
    args: {
        tooltipMessage: 'common.tooltip',
        children: <button className="px-4 py-2 bg-blue-500 text-white rounded">Hover me</button>,
    },
};

export const WithParams: Story = {
    args: {
        tooltipMessage: 'common.welcome',
        translationParams: { name: '<PERSON>' },
        children: <button className="px-4 py-2 bg-green-500 text-white rounded">Hover me</button>,
    },
};
