import type { Meta, StoryObj } from '@storybook/react';

import { Label } from '@/components/common/ui/Label';

const meta = {
    title: 'Components/Label',
    component: Label,
    tags: ['autodocs'],
    argTypes: {
        children: {
            description: 'Label text',
            control: 'text',
        },
        htmlFor: {
            description: 'Associated form element ID',
            control: 'text',
        },
    },
} satisfies Meta<typeof Label>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
    args: {
        children: 'Label',
    },
};

export const WithFormElement: Story = {
    args: {
        children: 'Email Address',
        htmlFor: 'email',
    },
    render: (args) => (
        <div className="space-y-2">
            <Label {...args} />
            <input id="email" type="email" placeholder="Enter email" className="w-full px-3 py-2 border rounded" />
        </div>
    ),
};
