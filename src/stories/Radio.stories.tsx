import type { <PERSON><PERSON>, StoryObj } from '@storybook/react-vite';
import { useState } from 'react';

// Import the actual Radio component from your UI library
import { Radio } from '@/components/common/ui/Radio';

const meta = {
    title: 'Components/Radio',
    component: Radio,
    parameters: {
        layout: 'centered',
        docs: {
            description: {
                component: `
<div style="max-height: 300px; overflow-y: auto; padding-right: 8px;">

<h1>Radio Component</h1>

A reusable, styled radio button component with a label. Built with React <strong>forwardRef</strong> for form library integration and automatic ID generation for accessibility.

<h2>Usage in Project</h2>

<h3>1. Filter Button - Sorting Method</h3>
<strong>File:</strong> src/components/common/filter-button/FilterButton.tsx  
- Used for selecting sorting order (ascending/descending)  
- Groups radio buttons with name="sorting-method"  
- Integrated with internationalization (i18next)

<h3>2. Filter Button - Sorting Data</h3>
<strong>File:</strong> src/components/common/filter-button/FilterButton.tsx  
- Used for selecting data field to sort by  
- Multiple options: Trip Code, Transit Number, Entry Port, Exit Port, etc.  
- Groups radio buttons with name="sorting-data"  
- All options use translation keys for multilingual support

<h3>3. Display Settings - Trip Display Mode</h3>
<strong>File:</strong> src/components/features/monitor-board/menu-taps/display-settings-tap/DisplaySettingsTap.tsx
- Used for selecting trip display mode (Map, List, etc.)
- Groups radio buttons with name="trip-display-mode"

<h2>Component Features</h2>
<ul>
<li><strong>Styled with Tailwind CSS</strong> - Consistent visual design</li>
<li><strong>Automatic ID generation</strong> - Uses React.useId() for accessibility</li>
<li><strong>Ref forwarding</strong> - Compatible with form libraries</li>
<li><strong>Hover effects</strong> - Interactive feedback with background changes</li>
<li><strong>Checked state styling</strong> - Blue border when selected</li>
<li><strong>Disabled state support</strong> - Opacity and cursor changes</li>
<li><strong>Custom onChange handler</strong> - Fires only when radio is selected</li>
</ul>

<h2>Design System Integration</h2>
- Uses consistent spacing and typography (text-[14px])  
- Hover states with gray-100 background  
- Blue border (border-blue-300) for checked state  
- Smooth transitions (transition-all duration-200)  
- Proper cursor states (pointer/not-allowed)  
- Flexible layout with gap-3 spacing  
</div>
                `,
            },
        },
    },
    tags: ['autodocs'],
    argTypes: {
        label: {
            control: 'text',
            description: 'The text label displayed next to the radio button',
        },
        name: {
            control: 'text',
            description: 'Name attribute for grouping radio buttons',
        },
        checked: {
            control: 'boolean',
            description: 'Whether the radio button is selected',
        },
        disabled: {
            control: 'boolean',
            description: 'Whether the radio button is disabled',
        },
        className: {
            control: 'text',
            description: 'Additional CSS classes to apply',
        },
        'aria-describedby': {
            control: 'text',
            description: 'ID of element that describes the radio button',
        },
        'aria-invalid': {
            control: 'boolean',
            description: 'Indicates if the radio button is in an error state',
        },
    },
    args: {
        name: 'radio-group',
        checked: false,
        disabled: false,
    },
} satisfies Meta<typeof Radio>;

export default meta;
type Story = StoryObj<typeof meta>;

// ============================================================================
// BASIC EXAMPLES
// ============================================================================

export const Default: Story = {
    args: {
        label: 'Default Radio',
        name: 'default-group',
    },
};

export const Checked: Story = {
    args: {
        label: 'Checked Radio',
        name: 'checked-group',
        checked: true,
    },
};

export const Disabled: Story = {
    args: {
        label: 'Disabled Radio',
        name: 'disabled-group',
        disabled: true,
    },
};

export const DisabledChecked: Story = {
    args: {
        label: 'Disabled Checked Radio',
        name: 'disabled-checked-group',
        disabled: true,
        checked: true,
    },
};

// ============================================================================
// RADIO GROUPS
// ============================================================================

// Radio group example component
const RadioGroupExample = () => {
    const [selected, setSelected] = useState<string>('option1');

    const options = [
        { value: 'option1', label: 'Option 1' },
        { value: 'option2', label: 'Option 2' },
        { value: 'option3', label: 'Option 3' },
    ];

    return (
        <div className="space-y-2">
            <h3 className="text-sm font-medium mb-3">Choose an option:</h3>
            {options.map((option) => (
                <Radio
                    key={option.value}
                    name="example-group"
                    label={option.label}
                    checked={selected === option.value}
                    onChange={() => setSelected(option.value)}
                />
            ))}
            <p className="text-xs text-gray-600 mt-3">Selected: {selected}</p>
        </div>
    );
};

export const RadioGroup: Story = {
    name: 'Radio Group Example',
    args: {
        label: 'Radio Group',
        name: 'example-group',
    },
    render: () => <RadioGroupExample />,
};

export const WithFieldset = () => {
    const [selected, setSelected] = useState<string>('');

    return (
        <fieldset className="space-y-3">
            <legend className="text-sm font-medium">Shipping Method</legend>
            <Radio
                name="shipping"
                label="Standard Shipping (3-5 days)"
                checked={selected === 'standard'}
                onChange={() => setSelected('standard')}
            />
            <Radio
                name="shipping"
                label="Express Shipping (1-2 days)"
                checked={selected === 'express'}
                onChange={() => setSelected('express')}
            />
            <Radio
                name="shipping"
                label="Overnight Shipping"
                checked={selected === 'overnight'}
                onChange={() => setSelected('overnight')}
            />
        </fieldset>
    );
};
WithFieldset.storyName = 'With Fieldset and Legend';

// ============================================================================
// ACCESSIBILITY EXAMPLES
// ============================================================================

export const WithDescription: Story = {
    args: {
        label: 'Email notifications',
        name: 'notifications',
        'aria-describedby': 'email-desc',
    },
    render: (args) => (
        <div className="space-y-2">
            <Radio {...args} />
            <div id="email-desc" className="text-sm text-gray-600">
                Receive important updates and notifications via email
            </div>
        </div>
    ),
    parameters: {
        docs: {
            description: {
                story: 'Radio button with descriptive text using aria-describedby for better accessibility.',
            },
        },
    },
};

export const WithError: Story = {
    args: {
        label: 'I agree to the terms',
        name: 'terms',
        'aria-invalid': true,
        'aria-describedby': 'terms-error',
    },
    render: (args) => (
        <div className="space-y-2">
            <Radio {...args} />
            <div id="terms-error" className="text-sm text-red-600">
                You must accept the terms to continue
            </div>
        </div>
    ),
    parameters: {
        docs: {
            description: {
                story: 'Radio button in error state with validation message.',
            },
        },
    },
};

export const KeyboardNavigation = () => {
    const [selected, setSelected] = useState<string>('');
    const options = ['Option A', 'Option B', 'Option C', 'Option D'];

    return (
        <fieldset className="space-y-2">
            <legend className="text-sm font-medium mb-3">Use Tab to navigate and Arrow keys to select:</legend>
            {options.map((option) => (
                <Radio
                    key={option}
                    name="keyboard-group"
                    label={option}
                    checked={selected === option}
                    onChange={() => setSelected(option)}
                />
            ))}
            <p className="text-xs text-gray-600 mt-3">Selected: {selected || 'None'}</p>
        </fieldset>
    );
};
KeyboardNavigation.storyName = 'Keyboard Navigation';

// ============================================================================
// FORM VALIDATION
// ============================================================================

export const FormValidation = () => {
    const [selected, setSelected] = useState<string>('');
    const [errors, setErrors] = useState<string>('');

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        if (!selected) {
            setErrors('Please select a payment method');
        } else {
            setErrors('');
            alert(`Selected: ${selected}`);
        }
    };

    return (
        <form onSubmit={handleSubmit} className="space-y-4">
            <fieldset className="space-y-2">
                <legend className="text-sm font-medium">Payment Method</legend>
                <Radio
                    name="payment"
                    label="Credit Card"
                    checked={selected === 'credit'}
                    onChange={() => setSelected('credit')}
                    aria-describedby={errors ? 'payment-error' : undefined}
                />
                <Radio
                    name="payment"
                    label="PayPal"
                    checked={selected === 'paypal'}
                    onChange={() => setSelected('paypal')}
                    aria-describedby={errors ? 'payment-error' : undefined}
                />
                <Radio
                    name="payment"
                    label="Bank Transfer"
                    checked={selected === 'bank'}
                    onChange={() => setSelected('bank')}
                    aria-describedby={errors ? 'payment-error' : undefined}
                />
                {errors && (
                    <div id="payment-error" className="text-sm text-red-600">
                        {errors}
                    </div>
                )}
            </fieldset>
            <button
                type="submit"
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                Continue
            </button>
        </form>
    );
};
FormValidation.storyName = 'Form Validation';

// ============================================================================
// EDGE CASES
// ============================================================================

export const LongLabel: Story = {
    args: {
        label: 'This is a radio button with a very long label that demonstrates how the component handles text wrapping and maintains proper alignment across multiple lines',
        name: 'long-label-group',
    },
    parameters: {
        docs: {
            description: {
                story: 'Demonstrates how the radio button handles long labels and text wrapping.',
            },
        },
    },
};

export const EmptyLabel: Story = {
    args: {
        label: '',
        name: 'empty-label-group',
        'aria-label': 'Radio button without visible label',
    },
    parameters: {
        docs: {
            description: {
                story: 'Radio button without visible label but with aria-label for accessibility.',
            },
        },
    },
};

export const ManyRadios = () => {
    const [selected, setSelected] = useState<string>('');
    const options = Array.from({ length: 20 }, (_, i) => `Option ${i + 1}`);

    return (
        <div className="max-h-64 overflow-y-auto space-y-1">
            <h3 className="text-sm font-medium mb-3">Performance test with many radio buttons:</h3>
            <fieldset>
                <legend className="sr-only">Select an option</legend>
                {options.map((option) => (
                    <Radio
                        key={option}
                        name="many-options"
                        label={option}
                        checked={selected === option}
                        onChange={() => setSelected(option)}
                    />
                ))}
            </fieldset>
            <p className="text-sm text-gray-600 mt-3">Selected: {selected || 'None'}</p>
        </div>
    );
};
ManyRadios.storyName = 'Many Radio Buttons (Performance)';

// ============================================================================
// CUSTOM STYLING
// ============================================================================

export const CustomStyling: Story = {
    args: {
        label: 'Custom Styled Radio',
        name: 'custom-group',
        className: 'bg-blue-50 border-blue-200 hover:bg-blue-100',
    },
};

export const CompactLayout = () => {
    const [selected, setSelected] = useState<string>('');

    return (
        <div className="space-y-1">
            <h3 className="text-sm font-medium mb-2">Compact Layout:</h3>
            {['Option 1', 'Option 2', 'Option 3'].map((option) => (
                <Radio
                    key={option}
                    name="compact-group"
                    label={option}
                    checked={selected === option}
                    onChange={() => setSelected(option)}
                    className="py-0.5"
                />
            ))}
        </div>
    );
};
CompactLayout.storyName = 'Compact Layout';

// ============================================================================
// REAL USAGE EXAMPLES
// ============================================================================

// Sorting method example component
const SortingMethodComponent = () => {
    const [sortingMethod, setSortingMethod] = useState<string>('');

    return (
        <div className="space-y-2">
            <h3 className="text-sm font-medium mb-3">Sorting Method:</h3>
            <Radio
                label="Descending"
                name="sorting-method"
                checked={sortingMethod === 'descending'}
                onChange={() => setSortingMethod('descending')}
            />
            <Radio
                label="Ascending"
                name="sorting-method"
                checked={sortingMethod === 'ascending'}
                onChange={() => setSortingMethod('ascending')}
            />
            <p className="text-xs text-gray-600 mt-3">Selected: {sortingMethod || 'None'}</p>
        </div>
    );
};

export const SortingMethodExample: Story = {
    name: 'Real Usage: Sorting Method',
    args: {
        label: 'Sorting Method',
        name: 'sorting-method',
    },
    render: () => <SortingMethodComponent />,
    parameters: {
        docs: {
            description: {
                story: 'This example shows how Radio buttons are used in FilterButton component (src/components/common/filter-button/FilterButton.tsx) for selecting sorting order.',
            },
        },
    },
};

// Sorting data example component
const SortingDataComponent = () => {
    const [sortingData, setSortingData] = useState<string>('');

    const dataFields = [
        { value: 'tripCode', label: 'Trip Code' },
        { value: 'transitNumber', label: 'Transit Number' },
        { value: 'entryPort', label: 'Entry Port' },
        { value: 'exitPort', label: 'Exit Port' },
        { value: 'transitDate', label: 'Transit Date' },
        { value: 'entryDate', label: 'Entry Date' },
        { value: 'exitDate', label: 'Exit Date' },
        { value: 'createdDate', label: 'Created Date' },
    ];

    return (
        <div className="space-y-2 max-w-xs">
            <h3 className="text-sm font-medium mb-3">Sort by:</h3>
            {dataFields.map((field) => (
                <Radio
                    key={field.value}
                    label={field.label}
                    name="sorting-data"
                    checked={sortingData === field.value}
                    onChange={() => setSortingData(field.value)}
                />
            ))}
            <p className="text-xs text-gray-600 mt-3">Selected: {sortingData || 'None'}</p>
        </div>
    );
};

export const SortingDataExample: Story = {
    name: 'Real Usage: Sorting Data Fields',
    args: {
        label: 'Sorting Data',
        name: 'sorting-data',
    },
    render: () => <SortingDataComponent />,
    parameters: {
        docs: {
            description: {
                story: 'This example replicates the Radio usage in FilterButton component for selecting which data field to sort by. All options are grouped under name="sorting-data".',
            },
        },
    },
};

export const TripDisplayMode = () => {
    const [displayMode, setDisplayMode] = useState<string>('map');

    const modes = [
        { value: 'map', label: 'Map View', description: 'Display trips on an interactive map' },
        { value: 'list', label: 'List View', description: 'Display trips in a table format' },
        { value: 'timeline', label: 'Timeline View', description: 'Display trips chronologically' },
    ];

    return (
        <fieldset className="space-y-3">
            <legend className="text-sm font-medium">Trip Display Mode:</legend>
            {modes.map((mode) => (
                <div key={mode.value} className="space-y-1">
                    <Radio
                        name="trip-display-mode"
                        label={mode.label}
                        checked={displayMode === mode.value}
                        onChange={() => setDisplayMode(mode.value)}
                    />
                    <p className="text-xs text-gray-600 ml-6">{mode.description}</p>
                </div>
            ))}
        </fieldset>
    );
};
TripDisplayMode.storyName = 'Real Usage: Trip Display Mode';

// ============================================================================
// COMPLEX EXAMPLES
// ============================================================================

export const ConditionalOptions = () => {
    const [userType, setUserType] = useState<string>('');
    const [notificationType, setNotificationType] = useState<string>('');

    const getNotificationOptions = () => {
        switch (userType) {
            case 'admin':
                return [
                    { value: 'all', label: 'All notifications' },
                    { value: 'critical', label: 'Critical alerts only' },
                    { value: 'system', label: 'System updates' },
                ];
            case 'user':
                return [
                    { value: 'important', label: 'Important updates' },
                    { value: 'weekly', label: 'Weekly summary' },
                ];
            default:
                return [];
        }
    };

    return (
        <div className="space-y-4">
            <fieldset className="space-y-2">
                <legend className="text-sm font-medium">User Type:</legend>
                <Radio
                    name="user-type"
                    label="Administrator"
                    checked={userType === 'admin'}
                    onChange={() => setUserType('admin')}
                />
                <Radio
                    name="user-type"
                    label="Regular User"
                    checked={userType === 'user'}
                    onChange={() => setUserType('user')}
                />
            </fieldset>

            {userType && (
                <fieldset className="space-y-2">
                    <legend className="text-sm font-medium">Notification Preferences:</legend>
                    {getNotificationOptions().map((option) => (
                        <Radio
                            key={option.value}
                            name="notification-type"
                            label={option.label}
                            checked={notificationType === option.value}
                            onChange={() => setNotificationType(option.value)}
                        />
                    ))}
                </fieldset>
            )}
        </div>
    );
};
ConditionalOptions.storyName = 'Conditional Options';

export const MultiStepForm = () => {
    const [step, setStep] = useState(1);
    const [formData, setFormData] = useState({
        accountType: '',
        plan: '',
        paymentMethod: '',
    });

    const handleNext = () => {
        if (step < 3) setStep(step + 1);
    };

    const handleBack = () => {
        if (step > 1) setStep(step - 1);
    };

    return (
        <div className="space-y-4 max-w-md">
            <div className="flex items-center gap-2 text-sm text-gray-600">
                <span
                    className={`w-6 h-6 rounded-full flex items-center justify-center ${step >= 1 ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}>
                    1
                </span>
                <span
                    className={`w-6 h-6 rounded-full flex items-center justify-center ${step >= 2 ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}>
                    2
                </span>
                <span
                    className={`w-6 h-6 rounded-full flex items-center justify-center ${step >= 3 ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}>
                    3
                </span>
            </div>

            {step === 1 && (
                <fieldset className="space-y-2">
                    <legend className="text-sm font-medium">Account Type:</legend>
                    <Radio
                        name="account-type"
                        label="Personal Account"
                        checked={formData.accountType === 'personal'}
                        onChange={() => setFormData((prev) => ({ ...prev, accountType: 'personal' }))}
                    />
                    <Radio
                        name="account-type"
                        label="Business Account"
                        checked={formData.accountType === 'business'}
                        onChange={() => setFormData((prev) => ({ ...prev, accountType: 'business' }))}
                    />
                </fieldset>
            )}

            {step === 2 && (
                <fieldset className="space-y-2">
                    <legend className="text-sm font-medium">Plan Selection:</legend>
                    <Radio
                        name="plan"
                        label="Basic Plan - $9/month"
                        checked={formData.plan === 'basic'}
                        onChange={() => setFormData((prev) => ({ ...prev, plan: 'basic' }))}
                    />
                    <Radio
                        name="plan"
                        label="Pro Plan - $19/month"
                        checked={formData.plan === 'pro'}
                        onChange={() => setFormData((prev) => ({ ...prev, plan: 'pro' }))}
                    />
                    <Radio
                        name="plan"
                        label="Enterprise Plan - $49/month"
                        checked={formData.plan === 'enterprise'}
                        onChange={() => setFormData((prev) => ({ ...prev, plan: 'enterprise' }))}
                    />
                </fieldset>
            )}

            {step === 3 && (
                <fieldset className="space-y-2">
                    <legend className="text-sm font-medium">Payment Method:</legend>
                    <Radio
                        name="payment-method"
                        label="Credit Card"
                        checked={formData.paymentMethod === 'credit'}
                        onChange={() => setFormData((prev) => ({ ...prev, paymentMethod: 'credit' }))}
                    />
                    <Radio
                        name="payment-method"
                        label="PayPal"
                        checked={formData.paymentMethod === 'paypal'}
                        onChange={() => setFormData((prev) => ({ ...prev, paymentMethod: 'paypal' }))}
                    />
                </fieldset>
            )}

            <div className="flex gap-2">
                {step > 1 && (
                    <button
                        type="button"
                        onClick={handleBack}
                        className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50 transition-colors">
                        Back
                    </button>
                )}
                {step < 3 ? (
                    <button
                        type="button"
                        onClick={handleNext}
                        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
                        Next
                    </button>
                ) : (
                    <button
                        type="button"
                        className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors">
                        Complete
                    </button>
                )}
            </div>
        </div>
    );
};
MultiStepForm.storyName = 'Multi-Step Form';
