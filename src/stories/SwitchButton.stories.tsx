import type { Meta, StoryObj } from '@storybook/react';

import SwitchButton from '@/components/common/ui/SwitchButton';

const meta: Meta<typeof SwitchButton> = {
    title: 'Components/SwitchButton',
    component: SwitchButton,
    tags: ['autodocs'],
    parameters: {
        layout: 'centered',
    },
    argTypes: {
        id: {
            description: 'Unique identifier for the switch',
            control: 'text',
        },
        label: {
            description: 'Translation key for the label',
            control: 'text',
        },
    },
};

export default meta;
type Story = StoryObj<typeof SwitchButton>;

export const Default: Story = {
    args: {
        id: 'default-switch',
        label: 'common.enabled',
    },
};

export const WithCustomLabel: Story = {
    args: {
        id: 'custom-switch',
        label: 'common.disabled',
    },
};
