import type { Meta, StoryObj } from '@storybook/react';

import { ChartContainer } from '@/components/common/ui/Chart';
import type { ChartConfig } from '@/components/common/ui/Chart';

const meta = {
    title: 'Components/Chart',
    component: ChartContainer,
    tags: ['autodocs'],
    argTypes: {
        config: {
            description: 'Chart configuration',
            control: 'object',
        },
        className: {
            description: 'Additional CSS classes',
            control: 'text',
        },
    },
} satisfies Meta<typeof ChartContainer>;

export default meta;
type Story = StoryObj<typeof meta>;

const sampleConfig: ChartConfig = {
    value: { color: '#3b82f6' },
    secondary: { color: '#10b981' },
};

export const Default: Story = {
    args: {
        config: sampleConfig,
        children: <div>Chart Content</div>,
    },
    render: (args) => (
        <ChartContainer {...args}>
            <div className="flex items-center justify-center h-32">
                <p className="text-sm text-gray-600">Chart content would go here</p>
            </div>
        </ChartContainer>
    ),
};
