import { useEffect, useRef, useState, useCallback } from 'react';
import { useMap } from '@vis.gl/react-google-maps';
import { useTranslation } from 'react-i18next';

import { useUIStore } from '@/stores/ui.store';
import type { Tool, UIStoreType } from '@/stores/ui.store';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
// Extend Window interface for Google Maps
declare global {
    interface Window {
        google: typeof google;
    }
}

interface MeasurementData {
    totalDistance: number;
    bearing: number;
    segments: Array<{
        distance: number;
        bearing: number;
        start: google.maps.LatLng;
        end: google.maps.LatLng;
    }>;
}

export function RulerTool() {
    const map = useMap();
    const { t } = useTranslation();
    const { dir } = useLocalized();
    const activeTool: Tool = useUIStore((s: UIStoreType) => s.activeTool);
    const setActiveTool = useUIStore((s) => s.setActiveTool);
    const isActive = activeTool === 'ruler';

    // measurement internals remain local
    const [measurementData, setMeasurementData] = useState<MeasurementData | null>(null);
    const [unit, setUnit] = useState<'metric' | 'imperial'>('metric');

    const pointsRef = useRef<google.maps.LatLng[]>([]);
    const markersRef = useRef<google.maps.Marker[]>([]);
    const linesRef = useRef<google.maps.Polyline[]>([]);
    const overlaysRef = useRef<google.maps.OverlayView[]>([]);
    const tickLinesRef = useRef<google.maps.Polyline[]>([]);

    // Professional distance label with horizontal text above the line
    class ProfessionalLabel extends google.maps.OverlayView {
        private position: google.maps.LatLng;
        private content: string;
        private bearing?: number;
        private isMainLabel: boolean;
        private div: HTMLDivElement | null = null;

        constructor(position: google.maps.LatLng, content: string, bearing?: number, isMainLabel = false) {
            super();
            this.position = position;
            this.content = content;
            this.bearing = bearing;
            this.isMainLabel = isMainLabel;
        }

        onAdd(): void {
            this.div = document.createElement('div');
            this.div.className = 'measurement-label';

            // Main label styling (comprehensive measurement box)
            if (this.isMainLabel) {
                this.div.style.cssText = `
                    position: absolute;
                    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
                    border: 2px solid #4a9eff;
                    border-radius: 8px;
                    padding: 12px 16px;
                    color: #ffffff;
                    font-family: 'Segoe UI', 'SF Pro Text', -apple-system, system-ui, sans-serif;
                    font-size: 13px;
                    font-weight: 600;
                    line-height: 1.4;
                    white-space: nowrap;
                    user-select: none;
                    pointer-events: none;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.1) inset;
                    backdrop-filter: blur(8px);
                    z-index: 1005;
                    min-width: 180px;
                    text-align: left;
                `;
            } else {
                // Segment label styling (compact and horizontal)
                this.div.style.cssText = `
                    position: absolute;
                    background: rgba(26, 26, 26, 0.95);
                    border: 1px solid #4a9eff;
                    border-radius: 6px;
                    padding: 6px 10px;
                    color: #ffffff;
                    font-family: 'Segoe UI', 'SF Pro Text', -apple-system, system-ui, sans-serif;
                    font-size: 11px;
                    font-weight: 500;
                    white-space: nowrap;
                    user-select: none;
                    pointer-events: none;
                    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
                    z-index: 1004;
                    text-align: center;
                `;
            }

            this.div.innerHTML = this.content;

            const panes = this.getPanes();
            if (panes?.overlayLayer) {
                panes.overlayLayer.appendChild(this.div);
            }
        }

        draw(): void {
            if (!this.div) return;

            const projection = this.getProjection();
            if (!projection) return;

            try {
                const point = projection.fromLatLngToDivPixel(this.position);
                if (point) {
                    // Calculate offset to position label perpendicular to the line (above it)
                    let offsetX = 0;
                    let offsetY = -40; // Default offset above the point

                    if (this.bearing !== undefined) {
                        // Calculate perpendicular offset to position label above the line
                        const bearingRad = (this.bearing * Math.PI) / 180;
                        const perpBearingRad = bearingRad - Math.PI / 2; // 90 degrees perpendicular (to the left of direction)
                        const aboveOffset = 40; // Distance above the line

                        offsetX = Math.cos(perpBearingRad) * aboveOffset;
                        offsetY = Math.sin(perpBearingRad) * aboveOffset;
                    }

                    // Position the label above the line, keeping text horizontal
                    this.div.style.left = `${point.x + offsetX}px`;
                    this.div.style.top = `${point.y + offsetY}px`;

                    // Keep label completely horizontal and centered
                    this.div.style.transform = `translate(-50%, -100%)`;
                    this.div.style.transformOrigin = 'center bottom';
                }
            } catch {
                // Silent error handling
            }
        }

        onRemove(): void {
            if (this.div?.parentNode) {
                this.div.parentNode.removeChild(this.div);
                this.div = null;
            }
        }
    }

    // Professional distance formatting
    const formatDistance = useCallback(
        (meters: number, showBoth = false): string => {
            if (unit === 'metric') {
                if (meters < 1000) {
                    const result = `${meters.toFixed(1)} m`;
                    return showBoth ? `${result} (${(meters * 3.28084).toFixed(1)} ft)` : result;
                } else if (meters < 10000) {
                    const km = meters / 1000;
                    const result = `${km.toFixed(3)} km`;
                    return showBoth ? `${result} (${((meters * 3.28084) / 5280).toFixed(3)} mi)` : result;
                } else {
                    const km = meters / 1000;
                    const result = `${km.toFixed(2)} km`;
                    return showBoth ? `${result} (${((meters * 3.28084) / 5280).toFixed(2)} mi)` : result;
                }
            } else {
                const feet = meters * 3.28084;
                if (feet < 5280) {
                    const result = `${feet.toFixed(1)} ft`;
                    return showBoth ? `${result} (${meters.toFixed(1)} m)` : result;
                } else {
                    const miles = feet / 5280;
                    const result = `${miles.toFixed(3)} mi`;
                    return showBoth ? `${result} (${(meters / 1000).toFixed(3)} km)` : result;
                }
            }
        },
        [unit],
    );

    // Format bearing
    const formatBearing = useCallback((bearing: number): string => {
        const normalized = ((bearing % 360) + 360) % 360;
        return `${normalized.toFixed(1)}°`;
    }, []);

    // Get cardinal direction
    const getCardinalDirection = useCallback((bearing: number): string => {
        const normalized = ((bearing % 360) + 360) % 360;
        const directions = [
            'N',
            'NNE',
            'NE',
            'ENE',
            'E',
            'ESE',
            'SE',
            'SSE',
            'S',
            'SSW',
            'SW',
            'WSW',
            'W',
            'WNW',
            'NW',
            'NNW',
        ];
        const index = Math.round(normalized / 22.5) % 16;
        return directions[index];
    }, []);

    // Enhanced tick spacing calculation for professional appearance
    const getTickSpacing = useCallback((totalDistance: number): number => {
        const spacingOptions = [
            1, 2, 5, 10, 20, 25, 50, 100, 200, 250, 500, 1000, 2000, 2500, 5000, 10000, 20000, 25000, 50000, 100000,
        ];

        const idealTicks = 6;
        const maxTicks = 12;
        const minTicks = 3;

        for (const spacing of spacingOptions) {
            const numTicks = Math.floor(totalDistance / spacing);
            if (numTicks >= minTicks && numTicks <= maxTicks) {
                if (numTicks <= idealTicks * 1.2) {
                    return spacing;
                }
            }
        }

        // Fallback calculation
        let baseSpacing = Math.pow(10, Math.floor(Math.log10(totalDistance / idealTicks)));
        const ratio = totalDistance / idealTicks / baseSpacing;

        if (ratio >= 5) baseSpacing *= 5;
        else if (ratio >= 2) baseSpacing *= 2;

        return Math.max(1, baseSpacing);
    }, []);

    // Calculate professional tick length
    const getTickLength = useCallback((totalDistance: number, zoom: number): number => {
        const baseLength = totalDistance * 0.02; // 2% of line length
        const zoomFactor = Math.pow(0.75, Math.max(0, zoom - 8));
        const tickLength = baseLength * zoomFactor;

        const minLength = Math.max(15, totalDistance * 0.008);
        const maxLength = Math.min(400, totalDistance * 0.05);

        return Math.max(minLength, Math.min(maxLength, tickLength));
    }, []);

    // Create simple total distance info
    const createMeasurementInfo = useCallback(
        (data: MeasurementData): string => {
            const distance = formatDistance(data.totalDistance);
            return distance;
        },
        [formatDistance],
    );

    // Clear all measurements
    const clearMeasurements = useCallback(() => {
        markersRef.current.forEach((marker) => marker.setMap(null));
        linesRef.current.forEach((line) => line.setMap(null));
        overlaysRef.current.forEach((overlay) => overlay.setMap(null));
        tickLinesRef.current.forEach((tick) => tick.setMap(null));

        markersRef.current = [];
        linesRef.current = [];
        overlaysRef.current = [];
        tickLinesRef.current = [];
        pointsRef.current = [];

        setMeasurementData(null);
    }, []);

    // Use isActive derived from store instead of local isActive state
    useEffect(() => {
        if (!map) return;
        const handleClick = (e: google.maps.MapMouseEvent) => {
            if (!isActive || !e.latLng) return;
            if (!window.google?.maps?.geometry?.spherical) return;

            const points = pointsRef.current;

            if (points.length === 0) {
                // First point - start measurement
                const marker = new google.maps.Marker({
                    position: e.latLng,
                    map,
                    icon: {
                        path: google.maps.SymbolPath.CIRCLE,
                        scale: 8,
                        fillColor: '#4a9eff',
                        fillOpacity: 1,
                        strokeColor: '#ffffff',
                        strokeWeight: 3,
                    },
                    zIndex: 1003,
                    title: t('common.startPoint'),
                });

                markersRef.current = [marker];
                pointsRef.current = [e.latLng];
            } else if (points.length === 1) {
                // Second point - complete measurement
                const endMarker = new google.maps.Marker({
                    position: e.latLng,
                    map,
                    icon: {
                        path: google.maps.SymbolPath.CIRCLE,
                        scale: 8,
                        fillColor: '#ff4757',
                        fillOpacity: 1,
                        strokeColor: '#ffffff',
                        strokeWeight: 3,
                    },
                    zIndex: 1003,
                    title: t('common.endPoint'),
                });
                markersRef.current.push(endMarker);

                // Create measurement line
                const line = new google.maps.Polyline({
                    path: [points[0], e.latLng],
                    map,
                    strokeColor: '#4a9eff',
                    strokeWeight: 4,
                    strokeOpacity: 0.8,
                    zIndex: 1002,
                    geodesic: true,
                });
                linesRef.current = [line];

                // Calculate measurements
                const distance = window.google.maps.geometry.spherical.computeDistanceBetween(points[0], e.latLng);
                const bearing = window.google.maps.geometry.spherical.computeHeading(points[0], e.latLng);
                const currentZoom = map.getZoom() || 10;

                const data: MeasurementData = {
                    totalDistance: distance,
                    bearing,
                    segments: [
                        {
                            distance,
                            bearing,
                            start: points[0],
                            end: e.latLng,
                        },
                    ],
                };

                setMeasurementData(data);

                const overlays: google.maps.OverlayView[] = [];
                const ticks: google.maps.Polyline[] = [];

                // Create professional tick marks (without labels)
                if (distance >= 20) {
                    const tickSpacing = getTickSpacing(distance);
                    const tickLength = getTickLength(distance, currentZoom);
                    const numTicks = Math.floor(distance / tickSpacing);

                    for (let i = 1; i <= numTicks; i++) {
                        const tickDistance = i * tickSpacing;
                        const fraction = tickDistance / distance;

                        // Skip ticks too close to endpoints
                        if (fraction < 0.08 || fraction > 0.92) continue;

                        const tickPoint = window.google.maps.geometry.spherical.interpolate(
                            points[0],
                            e.latLng,
                            fraction,
                        );
                        const halfTick = tickLength / 2;

                        // Create perpendicular tick mark
                        const tickStart = window.google.maps.geometry.spherical.computeOffset(
                            tickPoint,
                            halfTick,
                            bearing + 90,
                        );
                        const tickEnd = window.google.maps.geometry.spherical.computeOffset(
                            tickPoint,
                            halfTick,
                            bearing - 90,
                        );

                        const tickLine = new google.maps.Polyline({
                            path: [tickStart, tickEnd],
                            map,
                            strokeColor: '#4a9eff',
                            strokeWeight: 3,
                            strokeOpacity: 0.9,
                            zIndex: 1001,
                        });
                        ticks.push(tickLine);
                    }
                }

                // Only create total distance label above the end point (remove intermediate distance)
                const mainLabel = new ProfessionalLabel(e.latLng, createMeasurementInfo(data), bearing, false);
                mainLabel.setMap(map);
                overlays.push(mainLabel);

                overlaysRef.current = overlays;
                tickLinesRef.current = ticks;

                pointsRef.current.push(e.latLng);
            } else {
                // Third click - reset
                clearMeasurements();
            }
        };

        const clickListener = map.addListener('click', handleClick);
        return () => clickListener.remove();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [map, isActive, createMeasurementInfo, clearMeasurements, getTickSpacing, getTickLength, formatDistance]);

    // Update overlays when unit changes (same but uses measurementData)
    useEffect(() => {
        if (!measurementData || !map || pointsRef.current.length < 2) return;

        // Clear existing overlays
        overlaysRef.current.forEach((overlay) => overlay.setMap(null));
        overlaysRef.current = [];

        const points = pointsRef.current;
        const startPoint = points[0];
        const endPoint = points[points.length - 1];

        // Recalculate midpoint and bearing
        const bearing = window.google.maps.geometry.spherical.computeHeading(startPoint, endPoint);

        const overlays: google.maps.OverlayView[] = [];

        const mainLabel = new ProfessionalLabel(endPoint, createMeasurementInfo(measurementData), bearing, false);
        mainLabel.setMap(map);
        overlays.push(mainLabel);

        overlaysRef.current = overlays;
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [unit, measurementData, map, formatDistance, createMeasurementInfo]);

    // Cleanup on unmount
    useEffect(() => {
        return () => clearMeasurements();
    }, [clearMeasurements]);

    // Toggle now calls store API. Avoid calling if already matching state (prevent redundant writes)
    const handleToggle = useCallback(() => {
        if (isActive) {
            clearMeasurements();
            setActiveTool(null);
        } else {
            setActiveTool('ruler');
        }
    }, [isActive, setActiveTool, clearMeasurements]);

    // Clear measurements automatically when tool changes
    useEffect(() => {
        if (activeTool !== 'ruler') {
            clearMeasurements();
        }
    }, [activeTool, clearMeasurements]);

    const toggleUnit = useCallback(() => {
        setUnit((prev) => (prev === 'metric' ? 'imperial' : 'metric'));
    }, []);

    return (
        <div className="relative top-2.5 left-52 flex flex-col gap-2" dir={dir}>
            {/* Main Control Panel */}
            <div className="bg-white/95 backdrop-blur-sm  shadow border border-black-200 rounded-xs flex">
                {/* Distance Tool button */}
                <button
                    onClick={handleToggle}
                    className={`flex items-center gap-2 h-9 px-4 text-sm font-medium transition-colors border-r border-gray-200
  ${isActive ? 'bg-gray-100 text-black-600 font-bold' : 'text-gray-700 hover:bg-gray-50'}`}>
                    {dir === 'rtl' ? (
                        <>
                            {isActive ? t('common.stopMeasuring') : t('common.distanceTool')}
                            <span>📐</span>
                        </>
                    ) : (
                        <>
                            <span>📐</span>
                            {isActive ? t('common.stopMeasuring') : t('common.distanceTool')}
                        </>
                    )}
                </button>

                {/* Unit switch */}
                <button
                    onClick={toggleUnit}
                    className="px-3 h-9 text-xs font-medium text-gray-700 hover:bg-gray-50 transition-colors border-r border-gray-200"
                    title={unit === 'metric' ? t('common.switchToImperial') : t('common.switchToMetric')}>
                    {unit === 'metric' ? 'm/km' : 'ft/mi'}
                </button>

                {/* Clear button */}
                {isActive && measurementData && (
                    <button
                        onClick={clearMeasurements}
                        className="px-3 h-9 text-xs font-medium text-red-600 hover:bg-red-50 transition-colors"
                        title={t('common.clearMeasurement')}>
                        ✕
                    </button>
                )}
            </div>

            {/* Instructions */}
            {isActive && (
                <div className="bg-blue-50/95 backdrop-blur-sm border border-blue-200 border-black-200 rounded-xs p-3 text-sm">
                    <div className="text-blue-800 font-semibold mb-1">{t('common.distanceMeasurementActive')}</div>
                    <div className="text-blue-600 text-xs">
                        {pointsRef.current.length === 0
                            ? t('common.clickToSetStartPoint')
                            : pointsRef.current.length === 1
                              ? t('common.clickToSetEndPoint')
                              : t('common.clickAnywhereToStartNew')}
                    </div>
                </div>
            )}

            {isActive && measurementData && (
                <div className="bg-gray-900/95 backdrop-blur-sm border border-gray-600 border-black-200 rounded-sm p-3 text-white text-sm min-w-[200px]">
                    <div className="text-blue-400 font-semibold mb-2 text-xs">{t('common.measurementResults')}</div>
                    <div className="space-y-1">
                        <div className="flex justify-between">
                            <span className="text-gray-400">{t('common.totalDistance')}:</span>
                            <span className="font-semibold">{formatDistance(measurementData.totalDistance)}</span>
                        </div>
                        <div className="flex justify-between">
                            <span className="text-gray-400">{t('common.bearing')}:</span>
                            <span className="font-mono">
                                {formatBearing(measurementData.bearing)} {getCardinalDirection(measurementData.bearing)}
                            </span>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}
