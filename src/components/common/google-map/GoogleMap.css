

.google-map {
   position: relative;
   width: 100%;
   height: 100%
}

#map-container {
   border-radius: 10px;
   z-index: 3;
   border: 1px solid #e0e0e0;
   box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

 .map-loader {
   position: absolute;
   left: 50%;
   top: 50%;
   transform: translate(-50%, -50%);
   width: 44.8px;
   height: 44.8px;
   color: #554cb5;
   background: radial-gradient(11.2px, currentColor 94%, #0000);
   z-index: 4;
}

 .map-loader:before {
   content: '';
   position: absolute;
   inset: 0;
   border-radius: 50%;
   background: radial-gradient(10.08px at bottom right, #0000 94%, currentColor) top left,
      radial-gradient(10.08px at bottom left, #0000 94%, currentColor) top right,
      radial-gradient(10.08px at top right, #0000 94%, currentColor) bottom left,
      radial-gradient(10.08px at top left, #0000 94%, currentColor) bottom right;
   background-size: 22.4px 22.4px;
   background-repeat: no-repeat;
   animation: loader 1.5s infinite cubic-bezier(0.3, 1, 0, 1);
}


@keyframes loader {
   33% {
      inset: -11.2px;
      transform: rotate(0deg);
   }

   66% {
      inset: -11.2px;
      transform: rotate(90deg);
   }

   100% {
      inset: 0;
      transform: rotate(90deg);
   }
}
