import React from 'react';
import { InfoWindow } from '@vis.gl/react-google-maps';

import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { Icon, type IconName } from '@/components/common/ui/Icon';

/**
 * <PERSON>ps interface for the BaseInfoWindow component
 */
type BaseInfoWindowProps = {
    /** Geographic position coordinates for the info window */
    position: { lat: number; lng: number };
    /** icon name */
    iconName: IconName;
    /** Main title text displayed in the header */
    title: string;
    /** Optional subtitle text displayed below the title */
    subtitle?: string;
    /** Callback function triggered when the info window is closed */
    onClose: () => void;
    /** Header background color (hex string or named color) */
    headerColor?: string;
    /** Text color for title and subtitle */
    textColor?: string;
    /** Content to be rendered in the body of the info window */
    children: React.ReactNode;
};

/**
 * Mapping of header colors to their corresponding Tailwind CSS classes
 */
const HEADER_BG_CLASS: Record<string, string> = {
    '#175038': 'bg-[#175038]',
    '#1D526D': 'bg-[#1D526D]',
    '#0F4C81': 'bg-[#0F4C81]',
    '#DC2626': 'bg-[#DC2626]',
    // Ports
    '#1b8354': 'bg-[#1b8354]',
    '#179fca': 'bg-[#179fca]',
    '#fb963c': 'bg-[#fb963c]',
    '#fff': 'bg-white',
    white: 'bg-white',
};

/**
 * Mapping of text colors to their corresponding Tailwind CSS classes
 */
const TITLE_TEXT_CLASS: Record<string, string> = {
    '#fff': 'text-white',
    white: 'text-white',
    '#374151': 'text-gray-700',
    'gray-700': 'text-gray-700',
};

/**
 * BaseInfoWindow Component
 *
 * A reusable Google Maps InfoWindow component with customizable styling and RTL/LTR support.
 * Features a header with icon, title, and subtitle, plus a body section for custom content.
 *
 * @param props - Component properties
 * @returns JSX element representing the styled info window
 */
export function BaseInfoWindow({
    position,
    iconName: iconName,
    title,
    subtitle,
    onClose,
    headerColor = '#175038',
    textColor = 'white',
    children,
}: BaseInfoWindowProps) {
    const { dir } = useLocalized();
    const isRTL = dir === 'rtl';

    // Determine CSS classes based on provided colors
    const headerBgClass = HEADER_BG_CLASS[headerColor] ?? 'bg-[#175038]';
    const titleColorClass = TITLE_TEXT_CLASS[textColor] ?? 'text-white';
    const subtitleColorClass = titleColorClass === 'text-white' ? 'text-white/90' : 'text-gray-500';

    // Layout configuration for RTL/LTR support
    const rowDirClass = 'flex-row';
    const textAlignClass = isRTL ? 'text-right' : 'text-left';
    const safeSidePaddingClass = isRTL ? 'pr-3 pl-2' : 'pl-2 pr-3';
    const closeBtnPosClass = isRTL ? 'left-2.5' : 'right-2.5';

    /**
     * Renders the header icon with background styling
     */
    const icon = (
        <div className="w-10 h-10 flex items-center justify-center rounded-full" aria-hidden>
            <div className="w-8 h-8 flex items-center justify-center rounded-full bg-white/20 shadow-sm backdrop-blur-sm">
                <Icon name={iconName} className="w-6 h-6 filter brightness-0 invert" />
            </div>
        </div>
    );

    return (
        <InfoWindow
            position={position}
            onCloseClick={onClose}
            maxWidth={600}
            minWidth={360}
            headerDisabled
            pixelOffset={[0, -35]}>
            <div className="bg-white overflow-hidden shadow-lg rounded-lg m-0 p-0 border-0">
                {/* Header Section */}
                <div className={`relative py-2.5 ${headerBgClass}`} dir={dir}>
                    {/* Close Button */}
                    <button
                        onClick={onClose}
                        aria-label="Close"
                        className={`absolute top-2.5 ${closeBtnPosClass} w-5 h-5 flex items-center justify-center text-white hover:bg-black/20 rounded transition-all duration-200 z-10 text-2xl leading-none`}>
                        ×
                    </button>

                    {/* Header Content: Icon and Text */}
                    <div
                        className={`flex ${rowDirClass} items-center gap-3 w-full justify-start ${safeSidePaddingClass} py-0`}>
                        {/* Icon Container */}
                        <div className="flex-shrink-0">{icon}</div>

                        {/* Title and Subtitle Container */}
                        <div className={`flex flex-col ${textAlignClass} flex-1 min-w-0`}>
                            <h2 className={`text-sm font-semibold leading-tight ${titleColorClass} truncate`}>
                                {title}
                            </h2>
                            {subtitle && (
                                <p className={`text-xs mt-0.5 leading-tight ${subtitleColorClass} truncate`}>
                                    {subtitle}
                                </p>
                            )}
                        </div>
                    </div>
                </div>

                {/* Body Section */}
                <div className="px-3 py-2" dir={dir}>
                    {children}
                </div>
            </div>

            {/* Global Styles for Google Maps InfoWindow */}
            <style>{`
                .gm-style-iw {
                    padding: 0 !important;
                    margin: 0 !important;
                    border-radius: 12px !important;
                }
                .gm-style-iw > div {
                    padding: 0 !important;
                    margin: 0 !important;
                    border-radius: 12px !important;
                }
                .gm-style-iw-d {
                    padding: 0 !important;
                    margin: 0 !important;
                    overflow: visible !important;
                    border-radius: 12px !important;
                }
                .gm-style-iw-c {
                    border-radius: 12px !important;
                }
            `}</style>
        </InfoWindow>
    );
}
