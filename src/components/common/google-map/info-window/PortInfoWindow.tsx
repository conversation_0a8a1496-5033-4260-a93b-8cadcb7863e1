import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';

import { Icon, type IconName } from '@/components/common/ui/Icon';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import type { PortItem } from '@/infrastructure/api/ports/types';
import { usePortStatisticsStore, usePortStatisticsComputed } from '@/stores/port-statistics.store';
import { usePortsStore } from '@/stores/ports.store';
import { PortType } from '@/shared/enums';

import { BaseInfoWindow } from './BaseInfoWindow';

export type portTheme = {
    headerColor: string;
    textColor: string;
    iconName: IconName;
    statsIconBg: string;
    statsIconColor: string;
    subtitle: string;
};

const getPortTheme = (portType: PortType): portTheme => {
    switch (portType) {
        case PortType.LAND_PORT:
            return {
                headerColor: '#1b8354',
                textColor: 'white',
                iconName: 'truck-2',
                statsIconBg: 'bg-emerald-100',
                statsIconColor: 'text-emerald-600',
                subtitle: 'displaySettingsTab.landPort',
            };
        case PortType.SEA_PORT:
            return {
                headerColor: '#179fca',
                textColor: 'white',
                iconName: 'ship',
                statsIconBg: 'bg-sky-100',
                statsIconColor: 'text-sky-600',
                subtitle: 'displaySettingsTab.seaport',
            };
        case PortType.AIR_PORT:
            return {
                headerColor: '#fb963c',
                textColor: 'white',
                iconName: 'plane',
                statsIconBg: 'bg-orange-100',
                statsIconColor: 'text-orange-600',
                subtitle: 'displaySettingsTab.airport',
            };
        default:
            return {
                headerColor: '#6b7280',
                textColor: 'white',
                iconName: 'truck-2',
                statsIconBg: 'bg-gray-100',
                statsIconColor: 'text-gray-600',
                subtitle: 'displaySettingsTab.landPort',
            };
    }
};

function StatCard({
    iconName: iconName,
    value,
    label,
    theme,
    isLoading = false,
    error = false,
}: {
    iconName: IconName;
    value: string | number;
    label: string;
    theme: { statsIconBg: string; statsIconColor: string };
    isLoading?: boolean;
    error?: boolean;
}) {
    const displayValue = () => {
        if (error) return '⚠️';
        if (isLoading) return '...';
        return value;
    };

    const valueColor = () => {
        if (error) return 'text-red-500';
        if (isLoading) return 'text-gray-400';
        return 'text-gray-900';
    };

    return (
        <div className="flex flex-col items-center p-2 rounded-md bg-gray-50 shadow-sm">
            <div className={`w-10 h-10 rounded-md flex items-center justify-center mb-1 ${theme.statsIconBg}`}>
                {/* <img src={iconName} alt="" className={`w-5 h-5 ${theme.statsIconColor}`} /> */}
                <Icon name={iconName} className={`w-5 h-5 ${theme.statsIconColor}`} />
            </div>
            <div className={`text-sm font-semibold ${valueColor()}`}>{displayValue()}</div>
            <div className="text-xs text-gray-500 text-center">{label}</div>
        </div>
    );
}

// --- DETAIL ROW ---
function DetailRow({ label, value }: { label: string; value: React.ReactNode }) {
    return (
        <div className="flex justify-between items-center py-1">
            <span className="text-gray-600 text-xs">{label}</span>
            <span className="text-gray-800 text-sm font-medium">{value}</span>
        </div>
    );
}

export interface PortInfoWindowProps {
    port: PortItem;
    onClose: () => void;
}

// --- MAIN COMPONENT ---
export function PortInfoWindow({ port, onClose }: PortInfoWindowProps) {
    const { localized, dir } = useLocalized();
    const theme = getPortTheme(port.type);
    const { t } = useTranslation();

    // Port Statistics Store
    const { loadPortStatistics, statistics, isLoading, error } = usePortStatisticsStore();
    const { totalActiveTrips } = usePortStatisticsComputed();

    // Ports Store - for live contact data
    const { loadPorts, ports, isLoading: portsLoading } = usePortsStore();

    // Get live port data from store
    const livePortData = ports.find((p) => p.id === port.id);

    // Load statistics and ports data when component mounts or port changes
    useEffect(() => {
        if (port.id) {
            loadPortStatistics({ portId: port.id });
            // Load ports data to get fresh contact information
            loadPorts({ pageSize: 1000, pageNumber: 1 });
        }
    }, [port.id, loadPortStatistics, loadPorts]);

    // Extract geographic coordinates from port data
    const position = { lat: port.lat ?? 0, lng: port.long ?? 0 };
    return (
        <BaseInfoWindow
            position={position}
            iconName={theme.iconName}
            title={localized(port.name)}
            subtitle={t(theme.subtitle)}
            onClose={onClose}
            headerColor={theme.headerColor}
            textColor={theme.textColor}>
            <div className="space-y-3" dir={dir}>
                {/* Contact Info - Live Data */}
                <div className="space-y-2 border-b border-gray-200 pb-3">
                    <div className="flex justify-between items-center mb-2">
                        <h3 className="text-sm font-semibold text-gray-700">{t('checkpoints.contactInfo')}</h3>
                    </div>

                    {portsLoading ? (
                        <div className="p-2 text-xs text-gray-400 text-center">Loading...</div>
                    ) : livePortData?.contacts && livePortData.contacts.length > 0 ? (
                        <>
                            <DetailRow label="رقم الاتصال" value={livePortData.contacts[0].phone ?? '-'} />
                            <DetailRow label="المدير المسؤول" value={livePortData.contacts[0].name} />
                            <DetailRow label="نوع المدير" value={livePortData.contacts[0].type.name} />
                            <DetailRow label="البريد الإلكتروني" value={livePortData.contacts[0].email ?? '-'} />
                        </>
                    ) : (
                        <div className="p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                            <p className="text-xs text-yellow-600 text-center">{t('common.noContactInfo')}</p>
                        </div>
                    )}
                </div>

                {/* Live Statistics */}
                <div>
                    <div className="flex justify-between items-center mb-2">
                        <h3 className="text-sm font-semibold text-gray-700">{t('tripDetails.liveData')}</h3>
                    </div>
                    <div className="grid grid-cols-3 gap-2">
                        <StatCard
                            iconName={theme.iconName}
                            value={totalActiveTrips}
                            label={t('tripDetails.activeTrips')}
                            theme={theme}
                            isLoading={isLoading}
                            error={!!error}
                        />
                        <StatCard
                            iconName="gpsSignal"
                            value={statistics?.trackersCount ?? 0}
                            label={t('common.tracker')}
                            theme={theme}
                            isLoading={isLoading}
                            error={!!error}
                        />
                        <StatCard
                            iconName="lock"
                            value={statistics?.locksCount ?? 0}
                            label={t('tripDetails.elocks')}
                            theme={theme}
                            isLoading={isLoading}
                            error={!!error}
                        />
                    </div>

                    {/* Error Message */}
                    {error && (
                        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded-md">
                            <p className="text-xs text-red-600 text-center">{t('tripDetails.liveDataError')}</p>
                        </div>
                    )}
                </div>
            </div>
        </BaseInfoWindow>
    );
}
