import { useTranslation } from 'react-i18next';

import type { IconName } from '@/components/common/ui/Icon';
import type { MapPointDataItem } from '@/infrastructure/api/map-points/types';
import { useLocalized } from '@/shared/hooks/use-localized.hook';
import { MapPointEntryType } from '@/shared/enums';

import { BaseInfoWindow } from './BaseInfoWindow';

/**
 * Props interface for the CheckPointInfoWindow component
 */
type CheckPointInfoWindowProps = {
    /** Map point data containing location and contact information */
    point: MapPointDataItem;
    /** Callback function triggered when the info window is closed */
    onClose: () => void;
};

/**
 * Icon mappings for different checkpoint types
 * Maps each MapPointEntryType to its corresponding SVG icon path
 */
const ICONS: Record<MapPointEntryType, IconName> = {
    [MapPointEntryType.NONE]: 'customCheckpoint',
    [MapPointEntryType.CHECKPOINT]: 'customCheckpoint',
    [MapPointEntryType.POLICE_STATION]: 'customPoliceStation',
    [MapPointEntryType.SUSPECTED_AREA]: 'customPoliceStation',
};

/**
 * Header background color mappings for different checkpoint types
 * Each type has a distinct color to provide visual differentiation
 */
const HEADER_BG: Record<MapPointEntryType, string> = {
    /** Default/None - Dark Green */
    [MapPointEntryType.NONE]: '#175038',
    /** Regular Checkpoint - Dark Green */
    [MapPointEntryType.CHECKPOINT]: '#175038',
    /** Police Station - Dark Blue */
    [MapPointEntryType.POLICE_STATION]: '#1D526D',
    /** Suspected Area - Red (Warning) */
    [MapPointEntryType.SUSPECTED_AREA]: '#DC2626',
};

/**
 * CheckPointInfoWindow Component
 *
 * A specialized info window component for displaying checkpoint information on a map.
 * Supports multiple checkpoint types with different styling and icons.
 * Features RTL/LTR localization and displays contact information.
 *
 * @param props - Component properties
 * @returns JSX element representing the checkpoint info window
 */
export function CheckPointInfoWindow({ point, onClose }: CheckPointInfoWindowProps) {
    // Localization hooks for multi-language support
    const { localized, dir } = useLocalized();
    const { t } = useTranslation();

    // Extract CheckPointType from point data
    const CheckPointType = point.entryType;

    // Extract geographic coordinates from point data
    const position = { lat: point.lat ?? 0, lng: point.long ?? 0 };

    // Determine icon and styling based on checkpoint type
    const iconSrc = ICONS[CheckPointType];
    const headerColor = HEADER_BG[CheckPointType];

    /**
     * Generates the appropriate subtitle based on checkpoint type
     * Uses translation keys for internationalization support
     */
    const subtitle =
        CheckPointType === MapPointEntryType.POLICE_STATION
            ? t('checkpoints.policeStation')
            : CheckPointType === MapPointEntryType.CHECKPOINT
              ? t('checkpoints.checkpoint')
              : CheckPointType === MapPointEntryType.SUSPECTED_AREA
                ? t('checkpoints.suspectedArea')
                : t('checkpoints.default');

    return (
        <BaseInfoWindow
            position={position}
            iconName={iconSrc}
            title={localized(point.name)}
            subtitle={subtitle}
            onClose={onClose}
            headerColor={headerColor}
            textColor="white">
            {/* Contact Information Section */}
            <div className="space-y-2" dir={dir}>
                {/* Section Header */}
                <h3 className="text-sm font-semibold text-gray-800 pb-1">{t('checkpoints.contactInfo')}</h3>

                {/* Contact Details */}
                <div className="space-y-1.5">
                    {/* Contact Name Row */}
                    <div className="flex justify-between items-center">
                        <span className="text-xs text-gray-500">{t('checkpoints.contactName')}</span>
                        <span className="text-sm text-gray-800 font-medium">
                            {localized(point.contact?.name) ?? 'N/A'}
                        </span>
                    </div>

                    {/* Contact Phone Row */}
                    <div className="flex justify-between items-center">
                        <span className="text-xs text-gray-500">{t('checkpoints.contactPhone')}</span>
                        <span className="text-sm text-gray-800 font-medium">{point.contact?.phone ?? 'N/A'}</span>
                    </div>
                </div>
            </div>
        </BaseInfoWindow>
    );
}

export default CheckPointInfoWindow;
