// src/components/common/google-map/MapCameraFollower.tsx
/**
 * MapCameraFollower Component
 * -------------------------------------------------------------
 * Purpose:
 *   - Keeps the Google Map camera centered on the currently highlighted trip.
 *   - Automatically pans the map to the trip's latest location when its ID
 *     is marked as highlighted in the Zustand store.
 *   - Optionally adjusts the zoom level to a predefined value.
 *
 * Key Features:
 *   - Uses Zustand (`useTripLocationStore`) to read the `highlightedTripId`.
 *   - Reads the `VITE_TTS_HIGHLIGHT_ZOOM` value from `.env` (fallback: 14).
 *   - Smooth camera movement: `map.panTo()` centers the map,
 *     then `map.setZoom()` ensures consistent zoom.
 *   - Avoids redundant operations by checking the current zoom level.
 *   - Silent failure handling — prevents UI crashes if Google Maps API errors occur.
 *
 * Props:
 *   - trips: MapTrip[]
 *       List of available trips with their current locations.
 *
 * Behavior:
 *   - When `highlightedTripId` changes:
 *       → Find the matching trip from `trips`.
 *       → Pan the map to its latest `lat` / `long`.
 *       → Adjust zoom if different from the configured highlight zoom.
 *
 * Dependencies:
 *   - @vis.gl/react-google-maps → Provides `useMap` hook for Google Maps instance.
 *   - Zustand store (tripLocationStore) → Manages state for highlighted trips.
 *
 * Example Usage:
 *   <MapCameraFollower trips={mapTripsData} />
 *
 * Notes:
 *   - Designed as a "headless" component (returns `null`).
 *   - Purely side-effect-driven via `useEffect`.
 */

import { useEffect } from 'react';
import { useMap } from '@vis.gl/react-google-maps';

import { useTripLocationStore } from '@/stores/trip-location.store';
import { appConfig } from '@/shared/config/app-settings.config';
import { logger } from '@/infrastructure/logging';

import type { MapTrip } from './GoogleMap';

export function MapCameraFollower({ trips }: { trips: MapTrip[] }) {
    const map = useMap();
    const highlightedTripId = useTripLocationStore((s) => s.highlightedTripId);
    const HIGHLIGHT_ZOOM = appConfig.get('highlightZoom');

    useEffect(() => {
        if (!map || !highlightedTripId) return;

        const trip = trips.find((t) => t.id.toString() === highlightedTripId);
        if (!trip) return;

        try {
            // center then zoom — guard to avoid unnecessary calls
            const target = { lat: trip.currentLocation.lat, lng: trip.currentLocation.long };

            // If you want smooth-ish behavior, prefer panTo then setZoom
            // panTo may animate slightly depending on map config
            map.panTo(target);

            // only set zoom if different (avoids redundant cameraChanged events)
            const currentZoom = map.getZoom?.();
            if (typeof currentZoom === 'number' && currentZoom !== HIGHLIGHT_ZOOM) {
                map.setZoom(HIGHLIGHT_ZOOM);
            }
        } catch (err: unknown) {
            // silent failure — don't crash UI
            if (err instanceof Error) {
                logger.error('MapCameraFollower error', err);
            } else {
                logger.warn('MapCameraFollower error', { error: err });
            }
        }
    }, [map, highlightedTripId, trips, HIGHLIGHT_ZOOM]);

    return null;
}
