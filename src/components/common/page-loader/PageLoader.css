 

.main-page-loader-container {
   display: grid;
   place-items: center;
   height: 100%;
   width: 100%;
}

.main-page-loader-container .main-page-loader {
   --size: 60px;
  width: var(--size);
  height: var(--size);
  border-radius: 50%;
  display: inline-block;
  border-top: 3px solid #888;
  border-right: 3px solid transparent;
  box-sizing: border-box;
  animation: rotation .9s linear infinite;
}

@keyframes rotation {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
} 