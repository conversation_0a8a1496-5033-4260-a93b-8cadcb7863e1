import { format } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';

import { cn } from '@/shared/utils/class-name.utils';
import { Button } from '@/components/common/ui/Button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/common/ui/Popover';
import { Calendar } from '@/components/common/ui/Calendar';

interface DatePickerProps {
    value?: Date | null;
    onChange?: (date: Date | null) => void;
    placeholder?: string;
    className?: string;
    disabled?: boolean;
}

export function DatePicker({
    value,
    onChange,
    placeholder = 'Pick a date',
    className,
    disabled = false,
}: DatePickerProps) {
    return (
        <Popover>
            <PopoverTrigger asChild>
                <Button
                    variant="outline"
                    className={cn(
                        'w-full justify-start text-left font-normal',
                        !value && 'text-muted-foreground',
                        className,
                    )}
                    disabled={disabled}>
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {value ? format(value, 'dd/MM/yyyy') : placeholder}
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                    mode="single"
                    selected={value || undefined}
                    onSelect={(date) => onChange?.(date || null)}
                    required={false}
                />
            </PopoverContent>
        </Popover>
    );
}
