import { Editor } from 'primereact/editor';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { GoMail } from 'react-icons/go';

import { Button } from '@/components/common/ui/Button';
import {
    Dialog,
    DialogClose,
    DialogContent,
    DialogDescription,
    DialogFooter,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/common/ui/Dialog';
import { Input } from '@/components/common/ui/Input';
import { Label } from '@/components/common/ui/Label';
import { useSendEmailStore } from '@/stores/send-email.store';

interface EmailDialogProps {
    children?: React.ReactNode;
}

export default function EmailDialog({ children }: EmailDialogProps) {
    const { t } = useTranslation();
    const [isOpen, setIsOpen] = useState(false);

    // const isDataLoading = useSendEmailStore((state) => state.isDataLoading);
    const setEmail = useSendEmailStore((state) => state.setEmail);
    const setSubject = useSendEmailStore((state) => state.setSubject);
    const setDescription = useSendEmailStore((state) => state.setDescription);
    const submit = useSendEmailStore((state) => state.submit);

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>{children}</DialogTrigger>
            <DialogDescription></DialogDescription>
            <DialogContent className="sm:max-w-[500px] md:min-w-[700px]">
                <form>
                    <DialogHeader className="mb-3" dir="ltr">
                        <DialogTitle>
                            <div className="flex items-center gap-3 ">
                                <GoMail className="size-5 text-gray-500" />
                                {t('emailDialog.title')}
                            </div>
                        </DialogTitle>
                    </DialogHeader>

                    <div className="grid gap-4 py-4">
                        <div className="grid gap-2">
                            <Label htmlFor="email">{t('emailDialog.email')}</Label>
                            <Input
                                id="email"
                                type="email"
                                onChange={(e) => setEmail(e.target.value)}
                                placeholder={t('emailDialog.recipientEmail')}
                            />
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor="subject">{t('emailDialog.subject')}</Label>
                            <Input
                                id="subject"
                                type="text"
                                onChange={(e) => setSubject(e.target.value)}
                                placeholder={t('emailDialog.subjectPlaceholder')}
                            />
                        </div>

                        <div className="grid gap-2">
                            <Label htmlFor="description" className="mb-1">
                                {t('emailDialog.descriptionPlaceholder')}
                            </Label>
                            <Editor
                                dir="ltr"
                                placeholder={t('emailDialog.descriptionPlaceholder')}
                                onTextChange={(e) => setDescription(e.htmlValue ?? '')}
                                style={{ height: '200px' }}
                            />
                        </div>
                    </div>

                    <DialogFooter>
                        <DialogClose asChild>
                            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
                                {' '}
                                {t('common.cancel')}{' '}
                            </Button>
                        </DialogClose>

                        <Button
                            type="submit"
                            onClick={(e) => {
                                e.preventDefault();
                                submit();
                            }}>
                            {' '}
                            {t('common.send')}{' '}
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    );
}
