import React from 'react';

import { Icon } from './Icon';
import type { IconName } from './Icon';

interface TagProps {
    children: React.ReactNode;
    bgColor?: string; // custom background
    textColor?: string; // custom text color
    icon?: IconName;
    iconClassName?: string;
    className?: string; // <-- allow parent to pass extra classes
}

export function Tag({
    children,
    bgColor = 'bg-gray-100',
    textColor = 'text-gray-700',
    icon,
    iconClassName,
    className = '', // default empty
}: TagProps) {
    return (
        <span
            className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium ${bgColor} ${textColor} ${className}`}>
            {icon && <Icon name={icon} className={iconClassName ?? 'w-3 h-3'} />}
            {children}
        </span>
    );
}
