import { Area, Area<PERSON>hart, CartesianGrid, XAxis } from 'recharts';

import {
    ChartContainer,
    ChartLegend,
    ChartLegendContent,
    ChartTooltip,
    ChartTooltipContent,
    type ChartConfig,
} from '@/components/common/ui/Chart';

interface LineChartProps {
    chartConfig: ChartConfig;
    chartData: unknown[];
    dataKey: string;
}

export default function LineChart({ chartConfig, chartData, dataKey }: LineChartProps) {
    return (
        <>
            <ChartContainer config={chartConfig}>
                <AreaChart accessibilityLayer data={chartData} margin={{ left: 12, right: 12 }}>
                    <CartesianGrid vertical={false} />
                    <XAxis
                        dataKey={dataKey}
                        tickLine={false}
                        axisLine={false}
                        tickMargin={8}
                        tickFormatter={(value) => value.slice(0, 3)}
                    />
                    <ChartTooltip cursor={false} content={<ChartTooltipContent indicator="line" />} />
                    {chartConfig &&
                        Object.entries(chartConfig).map(([key, config]) => (
                            <Area
                                key={key}
                                dataKey={key}
                                fill={config.color}
                                stroke={config.color}
                                type="natural"
                                fillOpacity={0.4}
                                stackId="a"
                            />
                        ))}
                    <ChartLegend content={<ChartLegendContent />} />
                </AreaChart>
            </ChartContainer>
        </>
    );
}
