import { type ReactNode, forwardRef } from 'react';
import { useTranslation } from 'react-i18next';

import { LibTooltip, TooltipContent, TooltipTrigger } from '@/components/common/ui/LibTooltip';

interface ITooltip {
    children: ReactNode;
    tooltipMessage: string;
    translationParams?: { [key: string]: unknown };
}

const Tooltip = forwardRef<HTMLDivElement, ITooltip>(({ children, tooltipMessage, translationParams }, ref) => {
    const { t } = useTranslation();

    return (
        <div ref={ref}>
            <LibTooltip>
                <TooltipTrigger>{children}</TooltipTrigger>
                <TooltipContent> {t(tooltipMessage, translationParams)} </TooltipContent>
            </LibTooltip>
        </div>
    );
});
Tooltip.displayName = 'Tooltip';

export default Tooltip;
