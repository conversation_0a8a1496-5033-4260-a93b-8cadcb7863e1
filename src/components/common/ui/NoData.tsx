import { useTranslation } from 'react-i18next';

import emptyImage from '@imgs/empty.webp';

export default function NoData() {
    const { t } = useTranslation();

    return (
        <>
            <div className="max-w-[450px] grid place-items-center m-auto h-[100%]">
                <div className="text-center">
                    <img src={emptyImage} alt="Empty Image Indicator" width="100%" />
                    <p className="text-[18px] text-gray-400"> {t('common.noDataExist')} </p>
                </div>
            </div>
        </>
    );
}
