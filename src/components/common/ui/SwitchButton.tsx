import { useTranslation } from 'react-i18next';

import { Switch } from '@/components/common/ui/Switch';

interface SwitchButtonProps {
    id: string;
    label: string;
}

export default function SwitchButton({ id, label }: SwitchButtonProps) {
    const { t } = useTranslation();
    return (
        <div className="flex items-center space-x-2">
            <Switch id={id} />
            <label htmlFor={id} className="text-sm cursor-pointer">
                {' '}
                {t(label)}{' '}
            </label>
        </div>
    );
}
