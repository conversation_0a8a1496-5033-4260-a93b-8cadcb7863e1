// components/common/ExportExcelButton.tsx
import { Button } from 'primereact/button';
import { utils, write } from 'xlsx';
import { saveAs } from 'file-saver';

interface ExportExcelButtonProps<T = unknown> {
    data: T[]; // the table data to export
    fileName: string; // required filename (without extension)
    className?: string; // to allow design override
}

export const ExportExcelButton = <T,>({ data, fileName, className = '' }: ExportExcelButtonProps<T>) => {
    const exportExcel = () => {
        if (!data || data.length === 0) return;

        // 1. Convert JSON to sheet
        const worksheet = utils.json_to_sheet(data);

        // 2. Create workbook
        const workbook = { Sheets: { data: worksheet }, SheetNames: ['data'] };

        // 3. Write to buffer
        const excelBuffer = write(workbook, { bookType: 'xlsx', type: 'array' });

        // 4. Save file
        const blob = new Blob([excelBuffer], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8',
        });
        saveAs(blob, `${fileName}.xlsx`);
    };

    return (
        <Button
            onClick={exportExcel}
            className={`p-button-outlined bg-white border !border-green-600 rounded-sm  px-4 py-2 ${className}`}>
            {/* <Icon name="excel" className="w-7 h-7 text-green-700" />  */}
            <img src="src/assets/imgs/svg/excel.svg" alt="excel" className="w-7 h-7 text-green-700" />
        </Button>
    );
};
