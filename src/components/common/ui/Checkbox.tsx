import * as React from 'react';

/**
 * Props for the Checkbox component.
 * Extends native HTML input props (excluding `onChange` and `type`),
 * and adds a `label` for display, a custom `onChange` handler,
 * and an optional `isSelectAll` flag for "select all" behavior.
 */
interface CheckboxProps extends Omit<React.ComponentProps<'input'>, 'onChange' | 'type'> {
    /** The text label displayed next to the checkbox */
    label: string;

    /**
     * Optional callback triggered when the checkbox value changes.
     * Receives the new checked state (true or false).
     */
    onChange?: (checked: boolean) => void;

    /**
     * Optional flag to indicate if this checkbox is used as "Select All".
     * - Default: `false`
     * - When `true`, the label text is styled with the brand color `#1b8354`.
     */
    isSelectAll?: boolean;
}

/**
 * A reusable, styled checkbox component with a label.
 *
 * Features:
 * - Styled with Tailwind CSS using the brand accent `#1b8354`.
 * - Supports forwarding refs for integration with form libraries.
 * - Supports an optional "Select All" mode with distinct label styling.
 * - Maintains accessibility with `htmlFor` and keyboard support.
 *
 * Usage Example: Multi-select list
 * ```tsx
 * const items = ['Option 1', 'Option 2', 'Option 3'];
 * const [selected, setSelected] = React.useState<string[]>([]);
 *
 * const handleCheckboxChange = (label: string, checked: boolean) => {
 *   setSelected(prev =>
 *     checked ? [...prev, label] : prev.filter(item => item !== label)
 *   );
 * };
 *
 * return (
 *   {items.map(item => (
 *     <Checkbox
 *       key={item}
 *       label={item}
 *       checked={selected.includes(item)}
 *       onChange={(checked) => handleCheckboxChange(item, checked)}
 *     />
 *   ))}
 * );
 * ```
 */
const Checkbox = React.forwardRef<HTMLInputElement, CheckboxProps>(
    ({ label, onChange, isSelectAll = false, className = '', id, ...props }, ref) => {
        const generatedId = React.useId();
        const inputId = id ?? generatedId;

        const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
            onChange?.(e.target.checked);
        };

        return (
            <label
                htmlFor={inputId}
                className={`
          flex items-center gap-3 hover:bg-gray-100 py-1 px-2 rounded text-[14px]
          transition-all duration-200 cursor-pointer
          ${props.disabled ? 'opacity-50 cursor-not-allowed' : ''}
          ${className}
        `}>
                <input
                    id={inputId}
                    ref={ref}
                    type="checkbox"
                    onChange={handleChange}
                    className="w-3 h-3 accent-[#1b8354]"
                    {...props}
                />
                <span className={`flex-1 ${isSelectAll ? 'text-[#1b8354]' : ''}`}>{label}</span>
            </label>
        );
    },
);

Checkbox.displayName = 'Checkbox';

export { Checkbox };
