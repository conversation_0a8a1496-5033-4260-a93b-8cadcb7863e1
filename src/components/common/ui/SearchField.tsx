import { IoIosSearch } from 'react-icons/io';
import { useTranslation } from 'react-i18next';

export default function SearchField() {
    const { t } = useTranslation();

    return (
        <div className="flex items-center rounded-md bg-white pl-3 outline-1 -outline-offset-1 outline-gray-300 has-[input:focus-within]:outline-1 has-[input:focus-within]:-outline-offset-2 has-[input:focus-within]:outline-indigo-400">
            <IoIosSearch className="text-[#aaa] pointer-events-none col-start-1 row-start-1 mr-2 size-8 self-center justify-self-end  sm:size-4" />
            <input
                type="search"
                placeholder={t('common.search')}
                className="block min-w-0 grow py-1.5 pr-3 pl-1 text-base text-gray-900 placeholder:text-gray-400 focus:outline-none sm:text-sm/6"
            />
        </div>
    );
}
