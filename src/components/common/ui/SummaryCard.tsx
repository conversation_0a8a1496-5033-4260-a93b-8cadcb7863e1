import type { ReactElement } from 'react';

export interface SummaryCardDetails {
    title: string;
    icon: ReactElement<Element>;
    value: number;
}

export default function SummaryCard({ details, minWidth = 260 }: { details: SummaryCardDetails; minWidth?: number }) {
    return (
        <div className="flex items-center gap-5 p-2 px-3 border rounded-[8px] w-fit" style={{ minWidth }}>
            <span className="text-[30px]"> {details.icon} </span>

            <div>
                <p className="mb-1"> {details.title} </p>
                <p className="text-center text-[20px] font-semibold "> {details.value} </p>
            </div>
        </div>
    );
}
