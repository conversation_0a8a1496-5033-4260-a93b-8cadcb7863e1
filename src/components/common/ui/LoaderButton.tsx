/**
 * LoaderButton Component
 * -----------------------
 * A reusable button component with built-in support for a loading state.
 *
 * Features:
 * - Displays a loading spinner when `loading` is true.
 * - Automatically disables itself while loading to prevent duplicate actions.
 * - Supports multiple visual variants and sizes.
 * - Can be rendered as a native `<button>` or wrapped with Radix `Slot`.
 * - Consistent styling using `class-variance-authority` and TailwindCSS utilities.
 *
 * Example usage:
 * ```tsx
 * <LoaderButton
 *    loading={isSubmitting}
 *    isLoadingText="Submitting..."
 *    defaultText="Submit"
 *    icon={<SaveIcon />}
 *    variant="success"
 *    onClick={handleSubmit}
 * />
 * ```
 */

import * as React from 'react';
import { Slot } from '@radix-ui/react-slot';
import { cva } from 'class-variance-authority';
import { Loader2Icon } from 'lucide-react';

import { cn } from '@/shared/utils/class-name.utils';

/**
 * Button styling variants and sizes using CVA (Class Variance Authority).
 */
const buttonVariants = cva(
    "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
    {
        variants: {
            variant: {
                default: 'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',
                secondary: 'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',
                success: 'bg-[#1b8354] text-white shadow-xs hover:bg-[#1b8354]/90',
                outline:
                    'border bg-transparent shadow-xs hover:bg-accent text-[#1b8354] dark:bg-input/30 dark:border-input dark:hover:bg-input/50',
            },
            size: {
                default: 'h-9 px-4 py-2 has-[>svg]:px-3',
                sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',
                lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',
                icon: 'size-9',
            },
        },
        defaultVariants: {
            variant: 'default',
            size: 'default',
        },
    },
);

/**
 * Props for the LoaderButton component.
 */
export interface LoaderButtonProps extends React.ComponentProps<'button'> {
    /** Render the button as a child component using Radix Slot */
    asChild?: boolean;
    /** Whether the button is in a loading state */
    loading: boolean;
    /** Text displayed when the button is loading */
    isLoadingText: string;
    /** Default button text */
    defaultText: string;
    /** Optional icon displayed before the text */
    icon: React.ReactNode;
    /** Visual variant of the button */
    variant?: 'default' | 'success' | 'secondary' | 'outline';
    /** Size of the button */
    size?: 'default' | 'sm' | 'lg' | 'icon';
    /** Optional click handler */
    onClick?: () => void;
}

/**
 * LoaderButton Component
 */
const LoaderButton = React.forwardRef<HTMLButtonElement, LoaderButtonProps>(
    (
        {
            className,
            asChild = false,
            loading = false,
            isLoadingText = 'Applying',
            defaultText = 'Apply',
            icon,
            variant,
            size,
            onClick,
            ...props
        },
        ref,
    ) => {
        const Comp = asChild ? Slot : 'button';
        const currentText = loading ? isLoadingText : defaultText;

        return (
            <Comp
                data-slot="loader-button"
                className={cn(buttonVariants({ variant, size, className }))}
                disabled={loading}
                ref={ref}
                onClick={onClick}
                {...props}>
                {loading ? (
                    <>
                        <Loader2Icon className="animate-spin" />
                        {currentText}
                    </>
                ) : (
                    <>
                        {icon}
                        {currentText}
                    </>
                )}
            </Comp>
        );
    },
);

LoaderButton.displayName = 'LoaderButton';

export { LoaderButton };
