

.labeling-item.item {
   padding: 3px 10px;
   font-size: 13px;
   display: flex;
   flex-wrap: wrap;
   gap: 5px;
   justify-content: space-between;
   color: var(--main-font-color);
   font-weight: 500;
}

.labeling-item.item .label {
    color: #888;
   text-align: start;
   min-width: fit-content;
}

/*-------- Wrap Data Mode --------*/
.labeling-item.item.wrap-data {
    flex-wrap: wrap;
}

.labeling-item.item.wrap-data .label {
    flex: 1;
    text-align: start;
}

.labeling-item.item.wrap-data .data {
   text-align: center;
}
