import './LabelingItem.css';

export interface ILabelingItem {
    label: string;
    data: string;
    dataInNewLine?: boolean;
}

export default function LabelingItem(item: ILabelingItem) {
    return (
        <>
            <div className={`labeling-item item ${item.dataInNewLine ? 'wrap-data' : ''}`}>
                <span className="label"> {item.label} : </span>
                <span className="data"> {item.data} </span>
            </div>
        </>
    );
}
