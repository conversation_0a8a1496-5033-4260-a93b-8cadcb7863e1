import './FilterButton.css';
import { Accordion, AccordionTab } from 'primereact/accordion';
import { useState } from 'react';
import { IoArrowDownOutline, IoFilter, IoWarningSharp } from 'react-icons/io5';
import { MdFilterListAlt } from 'react-icons/md';
import { RiArrowLeftUpLine, RiResetLeftLine } from 'react-icons/ri';
import { FaTruck } from 'react-icons/fa';
import { FaCalendarDays } from 'react-icons/fa6';
import { useTranslation } from 'react-i18next';
import { Divider } from 'primereact/divider';

import { DatePicker } from '@/components/common/ui/DatePicker';
import { Button } from '@/components/common/ui/Button';
import { Input } from '@/components/common/ui/Input';
import { Checkbox } from '@/components/common/ui/Checkbox';
import { Radio } from '@/components/common/ui/Radio';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/common/ui/Dialog';

export default function FilterButton() {
    const { t } = useTranslation();
    // load warnings translation from warnings.json (lazy loading) and cache it
    const { t: tWarnings } = useTranslation('warnings'); // to use translation from warnings.json write tWarnings('warningKey') instead of t('warnings.warningKey')
    const [visible, setVisible] = useState(false);
    const [activeIndex, setActiveIndex] = useState<number | number[]>([0, 1, 2, 3, 4, 5]);
    const [startDate, setStartDate] = useState<Date | null>(null);
    const [endDate, setEndDate] = useState<Date | null>(null);
    const [transactionDate, setTransactionDate] = useState<Date | null>(null);

    const portList = [
        { name: t('ports.kingFahadIntlAirport'), isChecked: 'true' },
        { name: t('ports.jazanPort'), isChecked: 'true' },
        { name: t('ports.yanbuCommercialSeaport'), isChecked: 'true' },
        { name: t('ports.ruqaieBorder'), isChecked: 'true' },
        { name: t('ports.jeddahIslamicSeaport'), isChecked: 'true' },
        { name: t('ports.bathaCustomsBorder'), isChecked: 'true' },
        { name: t('ports.salwaCustomsBorder'), isChecked: 'true' },
        { name: t('ports.khafjiBorder'), isChecked: 'true' },
        { name: t('ports.halatAmmarCustoms'), isChecked: 'true' },
        { name: t('ports.durrahCustomsBorder'), isChecked: 'true' },
        { name: t('ports.dibaSeaportCustoms'), isChecked: 'true' },
        { name: t('ports.tuwalCustomsBorder'), isChecked: 'true' },
        { name: t('ports.wadiyahCustomsBorder'), isChecked: 'true' },
        { name: t('ports.albCustomsBorder'), isChecked: 'true' },
        { name: t('ports.khadraCustomsBorder'), isChecked: 'true' },
        { name: t('ports.kingFahadCauseway'), isChecked: 'true' },
        { name: t('ports.ararCustomsBorder'), isChecked: 'true' },
        { name: t('ports.hadithaCustomsBorder'), isChecked: 'true' },
        { name: t('ports.kingAbdulAzizAirport'), isChecked: 'true' },
        { name: t('ports.kingAbdullahPortCustom'), isChecked: 'true' },
        { name: t('ports.madinaCustomsBorder'), isChecked: 'true' },
        { name: t('ports.riyadhDryPort'), isChecked: 'true' },
        { name: t('ports.emptyQuarterCustoms'), isChecked: 'true' },
        { name: t('ports.unipartKingKhaledAirport'), isChecked: 'true' },
        { name: t('ports.binZagrKingAbdullahPort'), isChecked: 'true' },
        { name: t('ports.sabicNonActivatedArea'), isChecked: 'true' },
        { name: t('ports.nahdiMedicalCompany'), isChecked: 'true' },
        { name: t('ports.smsaCompany'), isChecked: 'true' },
        { name: t('ports.internationalMaritimeIndustries'), isChecked: 'true' },
        { name: t('ports.hailAirport'), isChecked: 'true' },
        { name: t('ports.taifAirport'), isChecked: 'true' },
        { name: t('ports.eastrenGateDepositaryArea'), isChecked: 'true' },
        { name: t('ports.bahriLogisticsDepositaryArea'), isChecked: 'true' },
        { name: t('ports.abhaAirport'), isChecked: 'true' },
        { name: t('ports.alAhsaAirport'), isChecked: 'true' },
        { name: t('ports.kingSalmanEnergyPark'), isChecked: 'true' },
        { name: t('ports.kingKhalidInternationalPort'), isChecked: 'true' },
        { name: t('ports.kingFahdIndustrialPortYanbu'), isChecked: 'true' },
        { name: t('ports.kingFahadCommercialPortJubail'), isChecked: 'true' },
        { name: t('ports.princeAbdulMohsinBinAbdulaziz'), isChecked: 'true' },
        { name: t('ports.kingFahdIndustrialPortDepositoryRedSea'), isChecked: 'true' },
        { name: t('ports.princeNaifBinAbdulazizIntlAirport'), isChecked: 'true' },
        { name: t('ports.princeSultanBinAbdulazizAirportTabuk'), isChecked: 'true' },
    ];

    const warnings = [
        { name: tWarnings('acknowledge'), isChecked: 'true' },
        { name: tWarnings('notAcknowledge'), isChecked: 'true' },
        { name: tWarnings('trackerTamper'), isChecked: 'true' },
        { name: tWarnings('trackerDropped'), isChecked: 'true' },
        { name: tWarnings('lockTamper'), isChecked: 'true' },
        { name: tWarnings('lockOpen'), isChecked: 'true' },
        { name: tWarnings('lockConnectionLost'), isChecked: 'true' },
        { name: tWarnings('trackerBatteryLow'), isChecked: 'true' },
        { name: tWarnings('lockLowBattery'), isChecked: 'true' },
        { name: tWarnings('lockVeryLowBattery'), isChecked: 'true' },
        { name: tWarnings('gsmSignalLost'), isChecked: 'true' },
        { name: tWarnings('gpsSignalLost'), isChecked: 'true' },
        { name: tWarnings('geofenceEntryBreach'), isChecked: 'true' },
        { name: tWarnings('geofenceExitBreach'), isChecked: 'true' },
        { name: tWarnings('trackerConnectionLost'), isChecked: 'true' },
        { name: tWarnings('tripDistanceExceeded'), isChecked: 'true' },
        { name: tWarnings('tripTimeExceeded'), isChecked: 'true' },
        { name: tWarnings('overSpeeding'), isChecked: 'true' },
        { name: tWarnings('truckStopped'), isChecked: 'true' },
        { name: tWarnings('wrongDirection'), isChecked: 'true' },
        { name: tWarnings('entryIntoCustomsArea'), isChecked: 'true' },
        { name: tWarnings('truckMoved'), isChecked: 'true' },
        { name: tWarnings('fourHoursExceeded'), isChecked: 'true' },
        { name: tWarnings('suspectedArea'), isChecked: 'true' },
    ];

    const [portsIn, setPortsIn] = useState(portList.map((p) => ({ ...p, isChecked: p.isChecked === 'false' })));
    const handlePortInChange = (index: number, checked: boolean) => {
        setPortsIn((prev) => {
            const copy = [...prev];
            copy[index] = { ...copy[index], isChecked: checked };
            return copy;
        });
    };

    function submit() {
        setVisible(false);
    }

    return (
        <>
            <Button
                size="sm"
                variant="secondary"
                className="border border-blue-600 text-blue-600"
                onClick={() => setVisible(true)}>
                <MdFilterListAlt />
                {t('common.filter')}
            </Button>
            {/* TODO: use TripFilterDialog instead from common/trip-filter-dialog */}
            <Dialog open={visible} onOpenChange={setVisible}>
                <DialogContent className="w-screen h-screen max-w-none max-h-none  p-0 filter-container">
                    <DialogHeader className="px-6 py-3 pb-0" dir="ltr">
                        <DialogTitle className="text-2xl text-blue-600 flex items-center gap-3">
                            <MdFilterListAlt /> {t('common.filter')}
                        </DialogTitle>
                    </DialogHeader>

                    <DialogDescription></DialogDescription>

                    <div className="overflow-y-auto px-3">
                        <Accordion
                            className="flex flex-wrap gap-3"
                            multiple
                            activeIndex={activeIndex}
                            onTabChange={(e) => setActiveIndex(e.index)}>
                            {/*------ Port In ------*/}
                            <AccordionTab
                                headerClassName="accordion-header"
                                className="w-[97vw]"
                                contentClassName="accordion-body"
                                header={
                                    <span className="flex items-center gap-3">
                                        {' '}
                                        <IoArrowDownOutline className="text-[18px] text-blue-500" />{' '}
                                        {t('common.portIn')}{' '}
                                    </span>
                                }>
                                <div className="grid md:grid-cols-4 lg:grid-cols-5 gap-x-3 gap-y-1">
                                    {portsIn.map((Item, index) => (
                                        // <label
                                        //     key={index}
                                        //     className="flex items-center gap-4 hover:bg-gray-100 py-1 px-2 rounded text-[14px]"
                                        //     htmlFor={'pornIn-' + index}>
                                        //     <span className="flex-grow-1"> {Item.name} </span>
                                        //     <input
                                        //         id={'pornIn-' + index}
                                        //         type="checkbox"
                                        //         onChange={(e) => (Item.isChecked = e.target.value)}
                                        //     />
                                        // </label>

                                        <Checkbox
                                            key={'pornIn-' + index}
                                            label={Item.name}
                                            checked={Item.isChecked}
                                            onChange={(checked) => handlePortInChange(index, checked)}
                                        />
                                    ))}
                                </div>
                            </AccordionTab>

                            {/*------ Port Out ------*/}
                            <AccordionTab
                                headerClassName="accordion-header"
                                className="w-[97vw]"
                                contentClassName="accordion-body"
                                header={
                                    <span className="flex items-center gap-3">
                                        {' '}
                                        <RiArrowLeftUpLine className="text-[18px] text-red-500" />{' '}
                                        {t('common.portOut')}{' '}
                                    </span>
                                }>
                                <div className="grid md:grid-cols-4 lg:grid-cols-5 gap-x-3 gap-y-1">
                                    {portList.map((Item, index) => (
                                        <label
                                            key={index}
                                            className="flex items-center gap-4 hover:bg-gray-100 py-1 px-2 rounded text-[14px]"
                                            htmlFor={'pornOut-' + index}>
                                            <span className="flex-grow-1"> {Item.name} </span>
                                            <input
                                                id={'pornOut-' + index}
                                                type="checkbox"
                                                onChange={(e) => (Item.isChecked = e.target.value)}
                                            />
                                        </label>
                                    ))}
                                </div>
                            </AccordionTab>

                            {/*------ Alerts ------*/}
                            <AccordionTab
                                headerClassName="accordion-header"
                                className="w-[97vw]"
                                contentClassName="accordion-body"
                                header={
                                    <span className="flex items-center gap-3">
                                        {' '}
                                        <IoWarningSharp className="text-[18px] text-red-500" />{' '}
                                        {t('filter.warnings')}{' '}
                                    </span>
                                }>
                                <div className="grid md:grid-cols-4 lg:grid-cols-5 gap-x-3 gap-y-1">
                                    {warnings.map((Item, index) => (
                                        <label
                                            key={index}
                                            className="flex items-center gap-4 hover:bg-gray-100 py-1 px-2 rounded text-[15px]"
                                            htmlFor={'warnings-' + index}>
                                            <span className="flex-grow-1"> {Item.name} </span>
                                            <input
                                                id={'warnings-' + index}
                                                type="checkbox"
                                                onChange={(e) => (Item.isChecked = e.target.value)}
                                            />
                                        </label>
                                    ))}
                                </div>
                            </AccordionTab>

                            {/*------ Truck ------*/}
                            <AccordionTab
                                headerClassName="accordion-header"
                                className="w-[97vw]"
                                contentClassName="accordion-body"
                                header={
                                    <span className="flex items-center gap-3">
                                        {' '}
                                        <FaTruck className="text-[18px] text-green-700" /> {t('filter.truckInfo')}{' '}
                                    </span>
                                }>
                                <div className="grid grid-cols-6 gap-x-5 gap-y-3">
                                    <div className="text-[15px]">
                                        <label className="mb-1 block"> {t('filter.transitNumber')} </label>
                                        <Input type="number" />
                                    </div>

                                    <div className="text-[15px]">
                                        <label className="mb-1 block"> {t('filter.transitSequenceNumber')} </label>
                                        <Input type="number" />
                                    </div>

                                    <div className="text-[15px]">
                                        <label className="mb-1 block"> {t('filter.driverName')} </label>
                                        <Input type="text" />
                                    </div>

                                    <div className="text-[15px]">
                                        <label className="mb-1 block"> {t('filter.plateNumber')} </label>
                                        <Input type="number" />
                                    </div>

                                    <div className="text-[15px]">
                                        <label className="mb-1 block"> {t('filter.trackerNumber')} </label>
                                        <Input type="number" />
                                    </div>

                                    <div className="text-[15px]">
                                        <label className="mb-1 block"> {t('filter.tripCode')} </label>
                                        <Input type="text" />
                                    </div>
                                </div>

                                <div className="flex gap-4 mt-5">
                                    <div className="flex-grow-1 text-[15px]">
                                        <span className="mb-1 block"> {t('filter.tripPriority')}: </span>
                                        <div className="flex items-center justify-between gap-4 px-3">
                                            <label
                                                className="flex items-center gap-4 hover:bg-gray-100 py-1 px-2 rounded text-[15px]"
                                                htmlFor="high-priority">
                                                <span className="flex-grow-1"> {t('filter.high')} </span>
                                                <input id="high-priority" type="checkbox" />
                                            </label>

                                            <label
                                                className="flex items-center gap-4 hover:bg-gray-100 py-1 px-2 rounded text-[15px]"
                                                htmlFor="medium-priority">
                                                <span className="flex-grow-1"> {t('filter.medium')} </span>
                                                <input id="medium-priority" type="checkbox" />
                                            </label>

                                            <label
                                                className="flex items-center gap-4 hover:bg-gray-100 py-1 px-2 rounded text-[15px]"
                                                htmlFor="low-priority">
                                                <span className="flex-grow-1"> {t('filter.low')} </span>
                                                <input id="low-priority" type="checkbox" />
                                            </label>
                                        </div>
                                    </div>

                                    <Divider layout="vertical" />

                                    <div className="flex-grow-1 text-[15px]">
                                        <span className="mb-1 block"> {t('filter.tripStatus')}: </span>
                                        <div className="flex items-center gap-4 px-3">
                                            <label
                                                className="flex items-center gap-4 hover:bg-gray-100 py-1 px-2 rounded text-[15px]"
                                                htmlFor="active-status">
                                                <span className="flex-grow-1"> {t('filter.active')} </span>
                                                <input id="active-status" type="checkbox" />
                                            </label>

                                            <label
                                                className="flex items-center gap-4 hover:bg-gray-100 py-1 px-2 rounded text-[15px]"
                                                htmlFor="ended-status">
                                                <span className="flex-grow-1"> {t('filter.ended')} </span>
                                                <input id="ended-status" type="checkbox" />
                                            </label>
                                        </div>
                                    </div>

                                    <Divider layout="vertical" />

                                    <div className="flex-grow-1 text-[15px]">
                                        <span className="mb-1 block"> {t('filter.tripLocation')}: </span>
                                        <div className="flex items-center gap-4 px-3">
                                            <label
                                                className="flex items-center gap-4 hover:bg-gray-100 py-1 px-2 rounded text-[15px]"
                                                htmlFor="on-route-location">
                                                <span className="flex-grow-1"> {t('filter.onRoute')} </span>
                                                <input id="on-route-location" type="checkbox" />
                                            </label>

                                            <label
                                                className="flex items-center gap-4 hover:bg-gray-100 py-1 px-2 rounded text-[15px]"
                                                htmlFor="exit-location">
                                                <span className="flex-grow-1"> {t('filter.inExitBorder')} </span>
                                                <input id="exit-location" type="checkbox" />
                                            </label>

                                            <label
                                                className="flex items-center gap-4 hover:bg-gray-100 py-1 px-2 rounded text-[15px]"
                                                htmlFor="entry-location">
                                                <span className="flex-grow-1"> {t('filter.inEntryBorder')} </span>
                                                <input id="entry-location" type="checkbox" />
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </AccordionTab>

                            {/*------ Date ------*/}
                            <AccordionTab
                                headerClassName="accordion-header"
                                className="w-[47.8vw]"
                                contentClassName="accordion-body"
                                header={
                                    <span className="flex items-center gap-3">
                                        {' '}
                                        <FaCalendarDays className="text-[15px] text-gray-400" /> {t('filter.date')}{' '}
                                    </span>
                                }>
                                <div className="text-[15px]">
                                    <label className="mb-1 block" htmlFor="transactionDate">
                                        {' '}
                                        {t('filter.transactionDate')}{' '}
                                    </label>
                                    <DatePicker
                                        value={transactionDate}
                                        onChange={setTransactionDate}
                                        placeholder="dd/mm/yyyy"
                                        className="w-full"
                                    />
                                </div>

                                <div className="grid grid-cols-2 gap-x-5 gap-y-1 mt-9 mb-3">
                                    <div className="text-[15px]">
                                        <label className="mb-1 block" htmlFor="startDate">
                                            {' '}
                                            {t('filter.startDate')}{' '}
                                        </label>
                                        <DatePicker
                                            value={startDate}
                                            onChange={setStartDate}
                                            placeholder="dd/mm/yyyy"
                                        />
                                    </div>

                                    <div className="text-[15px]">
                                        <label className="mb-1 block" htmlFor="endDate">
                                            {' '}
                                            {t('filter.endDate')}{' '}
                                        </label>
                                        <DatePicker value={endDate} onChange={setEndDate} placeholder="dd/mm/yyyy" />
                                    </div>
                                </div>
                            </AccordionTab>

                            {/*------ Sorting ------*/}
                            <AccordionTab
                                headerClassName="accordion-header"
                                className="w-[47.8vw]"
                                contentClassName="accordion-body"
                                header={
                                    <span className="flex items-center gap-3">
                                        {' '}
                                        <IoFilter className="text-[18px] text-red-500" /> {t('filter.orderBy')}{' '}
                                    </span>
                                }>
                                <div className="grid grid-cols-2 gap-x-5 gap-y-1">
                                    {/* <label
                                        className="flex items-center gap-4 hover:bg-gray-100 py-1 px-2 rounded"
                                        htmlFor="sorting-des">
                                        <span className="flex-grow-1"> {t('filter.descending')} </span>
                                        <input
                                            id="sorting-des"
                                            type="radio"
                                            onChange={(e) => e.target.value}
                                            name="sorting-method"
                                            className=""
                                        />
                                    </label>g

                                    <label
                                        className="flex items-center gap-4 hover:bg-gray-100 py-1 px-2 rounded text-[15px]"
                                        htmlFor="sorting-asd">
                                        <span className="flex-grow-1"> {t('filter.ascending')} </span>
                                        <input
                                            id="sorting-asd"
                                            type="radio"
                                            onChange={(e) => e.target.value}
                                            name="sorting-method"
                                        />
                                    </label> */}

                                    <Radio label={t('filter.descending')} name="sorting-method" />
                                    <Radio label={t('filter.ascending')} name="sorting-method" />
                                </div>

                                <hr className="my-2" />

                                <div className="grid grid-cols-2 gap-x-5 gap-y-1">
                                    {/* <label
                                        className="flex items-center gap-4 hover:bg-gray-100 py-1 px-2 rounded text-[15px]"
                                        htmlFor="tripCode">
                                        <span className="flex-grow-1"> {t('filter.tripCode')} </span>
                                        <input
                                            id="tripCode"
                                            type="radio"
                                            onChange={(e) => e.target.value}
                                            name="sorting-data"
                                        />
                                    </label>

                                    <label
                                        className="flex items-center gap-4 hover:bg-gray-100 py-1 px-2 rounded text-[15px]"
                                        htmlFor="entryPort">
                                        <span className="flex-grow-1"> {t('filter.entryPort')} </span>
                                        <input
                                            id="entryPort"
                                            type="radio"
                                            onChange={(e) => e.target.value}
                                            name="sorting-data"
                                        />
                                    </label>

                                    <label
                                        className="flex items-center gap-4 hover:bg-gray-100 py-1 px-2 rounded text-[15px]"
                                        htmlFor="exitPort">
                                        <span className="flex-grow-1"> {t('filter.exitPort')} </span>
                                        <input
                                            id="exitPort"
                                            type="radio"
                                            onChange={(e) => e.target.value}
                                            name="sorting-data"
                                        />
                                    </label>

                                    <label
                                        className="flex items-center gap-4 hover:bg-gray-100 py-1 px-2 rounded text-[15px]"
                                        htmlFor="transitNo">
                                        <span className="flex-grow-1"> {t('filter.transitNo')} </span>
                                        <input
                                            id="transitNo"
                                            type="radio"
                                            onChange={(e) => e.target.value}
                                            name="sorting-data"
                                        />
                                    </label>

                                    <label
                                        className="flex items-center gap-4 hover:bg-gray-100 py-1 px-2 rounded text-[15px]"
                                        htmlFor="transitDate">
                                        <span className="flex-grow-1"> {t('filter.transitDate')} </span>
                                        <input
                                            id="transitDate"
                                            type="radio"
                                            onChange={(e) => e.target.value}
                                            name="sorting-data"
                                        />
                                    </label>

                                    <label
                                        className="flex items-center gap-4 hover:bg-gray-100 py-1 px-2 rounded text-[15px]"
                                        htmlFor="entryDate">
                                        <span className="flex-grow-1"> {t('filter.entryDate')} </span>
                                        <input
                                            id="entryDate"
                                            type="radio"
                                            onChange={(e) => e.target.value}
                                            name="sorting-data"
                                        />
                                    </label>

                                    <label
                                        className="flex items-center gap-4 hover:bg-gray-100 py-1 px-2 rounded text-[15px]"
                                        htmlFor="exitDate">
                                        <span className="flex-grow-1"> {t('filter.exitDate')} </span>
                                        <input
                                            id="exitDate"
                                            type="radio"
                                            onChange={(e) => e.target.value}
                                            name="sorting-data"
                                        />
                                    </label>

                                    <label
                                        className="flex items-center gap-4 hover:bg-gray-100 py-1 px-2 rounded text-[15px]"
                                        htmlFor="createdDate">
                                        <span className="flex-grow-1"> {t('filter.createdDate')} </span>
                                        <input
                                            id="createdDate"
                                            type="radio"
                                            onChange={(e) => e.target.value}
                                            name="sorting-data"
                                        />
                                    </label> */}

                                    <Radio label={t('filter.tripCode')} name="sorting-data" />
                                    <Radio label={t('filter.transitNumber')} name="sorting-data" />
                                    <Radio label={t('filter.entryPort')} name="sorting-data" />
                                    <Radio label={t('filter.exitPort')} name="sorting-data" />
                                    <Radio label={t('filter.transitDate')} name="sorting-data" />
                                    <Radio label={t('filter.entryDate')} name="sorting-data" />
                                    <Radio label={t('filter.exitDate')} name="sorting-data" />
                                    <Radio label={t('filter.createdDate')} name="sorting-data" />
                                </div>
                            </AccordionTab>
                        </Accordion>
                    </div>

                    <div className="ms-auto pb-4 px-10" dir="ltr">
                        <Button variant="outline" className="mx-3">
                            {' '}
                            <RiResetLeftLine /> {t('filter.reset')}{' '}
                        </Button>
                        <Button onClick={() => submit()}>
                            {' '}
                            <MdFilterListAlt /> {t('filter.applyFilter')}{' '}
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        </>
    );
}
