:root {
    --accordion-radius: 26px;
    --active-green: #1b8354;
    --light-green-bg: rgba(27, 131, 84, 0.06); /* light background */
}

.filter-container .accordion-header a {
    padding: 12px 1rem;
    font-size: 15px;
    border-color: transparent;
    box-shadow: 0px -3px 10px #33333315 inset;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.filter-container .accordion-header a .p-accordion-toggle-icon {
    display: none;
}

.filter-container .accordion-body .p-accordion-content {
    padding: 10px;
    border-radius: 0 0 var(--accordion-radius) var(--accordion-radius);
    background: var(--light-color);
}

.filter-container label {
    transition: 0.2s;
    cursor: pointer;
}

.filter-container {
    border-radius: 16px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    background: #fff;
}

/* 1) Accordion header when the tab is open (PrimeReact sets aria-expanded on anchor) */
.filter-container .accordion-header a[aria-expanded='true'] {
    background: var(--light-green-bg);
    color: var(--active-green);
    /* Enhance text visibility */
    font-weight: 800;
}

/* 2) Inside the header: force SVG icons to inherit green color when open or always */
.filter-container .accordion-header a[aria-expanded='true'] svg,
.filter-container .accordion-header span.text-active-green svg,
.filter-container .filter-footer svg {
    color: var(--active-green) !important;
}
