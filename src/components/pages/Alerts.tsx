import { useTranslation } from 'react-i18next';
import { BsFillInfoCircleFill } from 'react-icons/bs';
import { FaEye, FaEyeSlash } from 'react-icons/fa';
import { HiLockClosed } from 'react-icons/hi2';
import { IoMdCheckmarkCircleOutline, IoMdDownload } from 'react-icons/io';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { MdBattery90, MdCheck, MdOutlineSignalCellularAlt } from 'react-icons/md';
import { IoWifiSharp } from 'react-icons/io5';

import Charging from '@imgs/svg/charging.svg';
import Tooltip from '@/components/common/ui/Tooltip';
import FilterButton from '@/components/common/filter-button/FilterButton';
import SummaryCard, { type SummaryCardDetails } from '@/components/common/ui/SummaryCard';
import { Button } from '@/components/common/ui/Button';
import { useAlertStore } from '@/stores/alert.store';

export default function Alerts() {
    const { t } = useTranslation();

    const tableData = useAlertStore((state) => state.alerts);

    const summaryCardsDetails: SummaryCardDetails[] = [
        {
            title: t('common.totalActiveTrips'),
            icon: <IoMdCheckmarkCircleOutline />,
            value: 1500,
        },
        { title: t('common.closedTrips'), icon: <HiLockClosed />, value: 18 },
        { title: t('common.viewedTrips'), icon: <FaEye />, value: 12 },
        { title: t('common.notViewedTrips'), icon: <FaEyeSlash />, value: 1350 },
    ];

    return (
        <section className="flex flex-col pt-4">
            <section className="flex items-center justify-between flex-wrap px-4 gap-3">
                <div className="flex gap-4 mb-4 flex-grow-1">
                    {summaryCardsDetails.map((item, index) => (
                        <SummaryCard details={item} minWidth={200} key={index} />
                    ))}
                </div>

                <div className="flex flex-wrap justify-end gap-3 pe-3 flex-grow-1 mb-4">
                    <Button size="sm" variant="default" className="border">
                        <IoMdDownload />
                        {t('common.downloadTripPanelAsXLS')}
                    </Button>
                    <FilterButton />
                </div>
            </section>

            <div className="_table_container flex-grow-1">
                <DataTable
                    value={tableData}
                    tableStyle={{ minWidth: '50rem' }}
                    scrollable
                    scrollHeight="62vh"
                    paginator
                    rows={50}>
                    <Column
                        body={(rowData) => (
                            <Tooltip tooltipMessage={rowData.alert.details || '-'}>
                                <BsFillInfoCircleFill className="size-4 text-blue-600 min-w-fit m-auto" />
                            </Tooltip>
                            // <Tooltip>
                            //   <TooltipTrigger>
                            //     <BsFillInfoCircleFill className="size-4.5 text-blue-600 min-w-fit m-auto" />
                            //   </TooltipTrigger>
                            //   <TooltipContent>{rowData.alert.details || "-"}</TooltipContent>
                            // </Tooltip>
                        )}
                        header={t('common.alertId')}></Column>
                    <Column body={(rowData) => rowData.alert.type || '-'} header={t('common.alertType')}></Column>
                    <Column
                        body={(rowData) => rowData.transitNumber || '-'}
                        header={t('common.transitNumber')}></Column>
                    <Column body={(rowData) => rowData.routeName || '-'} header={t('common.routeName')}></Column>
                    <Column
                        body={(rowData) => rowData.shipmentDescription || '-'}
                        header={t('common.shipmentDescription')}></Column>
                    <Column body={(rowData) => rowData.timestamp || '-'} header={t('common.timestamp')}></Column>
                    <Column body={(rowData) => rowData.address || '-'} header={t('common.address')}></Column>
                    <Column
                        body={() => (
                            <div className="flex items-center justify-center gap-1">
                                <Tooltip tooltipMessage="filter.active">
                                    <MdCheck className="m-auto text-[green] size-5" />
                                </Tooltip>

                                <Tooltip tooltipMessage="common.batteryLife" translationParams={{ value: '90%' }}>
                                    <MdBattery90 className="m-auto  size-5" />
                                </Tooltip>

                                <Tooltip tooltipMessage={`GPS 60%`}>
                                    <IoWifiSharp className="m-auto  size-5" />
                                </Tooltip>

                                <Tooltip tooltipMessage={`GSM 70%`}>
                                    <MdOutlineSignalCellularAlt className="m-auto  size-5" />
                                </Tooltip>

                                <Tooltip tooltipMessage="common.noCharging">
                                    <img
                                        src={Charging}
                                        alt="Charging"
                                        width="20px"
                                        height="20px"
                                        style={{ maxWidth: 'fit-content' }}
                                    />
                                </Tooltip>
                            </div>
                        )}
                        header={t('common.alertStatus')}></Column>
                </DataTable>
            </div>
        </section>
    );
}
