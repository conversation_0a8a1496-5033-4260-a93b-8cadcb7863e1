.login-form-container {
    margin: auto;
    display: grid;
    place-items: center;
    height: 100%;
}

.details-container {
    text-align: center;
    padding: 1rem;
    max-width: 350px;
}

.login-page-container {
    --bubble_color: blue;
    position: relative;
    overflow: hidden;
    width: 100vw;
    height: 100vh;
    background: url(../../../assets/imgs/overlay.png);
    background-position: center;
    direction: ltr;

    &:before,
    &:after {
        content: '';
        position: absolute;
        top: -250px;
        right: -250px;
        width: 600px;
        height: 600px;
        background: linear-gradient(45deg, var(--bubble_color), transparent);
        border-radius: 212px;
        rotate: 12deg;
        box-shadow:
            -10px 14px 30px rgba(0, 0, 0, 0.1607843137),
            11px -7px 47px #bb101091 inset;
        z-index: -1;
    }

    &:after {
        top: 40%;
        left: -18%;
        background: linear-gradient(45deg, transparent, var(--bubble_color));
        box-shadow:
            10px -14px 30px #00000029,
            -24px 25px 47px #bb101091 inset;
    }

    .logo {
        max-width: 500px;
        object-fit: cover;
        margin-bottom: 2rem;
    }
}

.welcome {
    font-size: 1.9rem;
    background: -webkit-linear-gradient(rgba(1, 1, 47, 0.205), var(--bubble_color));
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.title {
    margin-block: 10px 2rem;
    font-size: 1.4rem;
    font-weight: 600;
}

@media (width < 1100px) {
    .details-container {
        background: #ffffff5c;
        padding: 1.5rem;
        border-radius: 31px;
        backdrop-filter: blur(4px);
        border: 1px solid #aaaaaa3d;
        margin: 1rem;
    }

    .login-page-container::after {
        left: -250px;
    }

    .logo {
        width: 140px;
    }
}

.login-button {
    background: linear-gradient(0deg, var(--bubble_color), rgba(42, 42, 212, 0.868));

    &:hover {
        color: #f2f2f2;
    }
}

 