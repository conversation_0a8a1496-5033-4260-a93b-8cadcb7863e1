import './Login.css';
import { useTranslation } from 'react-i18next';

import logo from '@imgs/logo.png';

import LoginForm from '../../features/login/LoginForm';
import LanguageButton from '../../features/layout/navbar/LanguageButton';

export default function Login() {
    const { t } = useTranslation();

    return (
        <section className="login-page-container">
            <div className="login-form-container">
                <div className="details-container">
                    <img src={logo} alt="logo" className="logo" />

                    <h5 className="welcome"> {t('login.welcomeBack')} </h5>
                    <h5 className="title"> {t('login.login')} </h5>
                    <LoginForm />
                </div>

                <div className="mt-4">
                    <LanguageButton withLabel={true} />
                </div>
            </div>
        </section>
    );
}
