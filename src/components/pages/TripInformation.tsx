import { useTranslation } from 'react-i18next';
import { <PERSON>a<PERSON>ye, FaMapMarkedAlt } from 'react-icons/fa';
import { Fc<PERSON><PERSON>, FcVlc } from 'react-icons/fc';
import { IoMdWarning } from 'react-icons/io';
import { MdListAlt, MdOutlineTimeline } from 'react-icons/md';
import { TbTimelineEventText } from 'react-icons/tb';

import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/common/ui/Tabs';

import TripActivitiesReportView from '../features/trip-information/TripActivitiesReportView';
import TripAlertsViewersView from '../features/trip-information/TripAlertsViewersView';
import TripDetails from '../features/trip-information/TripDetails';
import TripEventsReportView from '../features/trip-information/TripEventsReportView';
import TripMovementReportView from '../features/trip-information/TripMovementReportView';
import TripPingsView from '../features/trip-information/TripPingsView';
import TripViewersView from '../features/trip-information/TripViewersView';
import TripWarningsView from '../features/trip-information/TripWarningsView';
import TripMap from '../features/trip-information/TripMap';

export default function TripInformation() {
    const { t } = useTranslation();

    return (
        <section className="pt-2">
            <Tabs defaultValue="TripMap">
                <div className="flex gap-2 h-[100%] rtl:flex-row-reverse">
                    <div className="max-h-[83vh] max-w-[400px]">
                        <TripDetails />
                    </div>

                    <div className="flex flex-col w-full">
                        <TabsList className="w-[99%] m-auto mb-3 rtl:flex-row-reverse">
                            <TabsTrigger value="TripMap">
                                {' '}
                                <FaMapMarkedAlt className="text-red-400" /> {t('common.tripMap')}{' '}
                            </TabsTrigger>
                            <TabsTrigger value="TripWarnings">
                                {' '}
                                <IoMdWarning className="text-red-500" /> {t('common.warnings')}{' '}
                            </TabsTrigger>
                            <TabsTrigger value="TripPings">
                                {' '}
                                <FcVlc /> {t('common.pings')}{' '}
                            </TabsTrigger>
                            <TabsTrigger value="MovementReport">
                                {' '}
                                <MdListAlt /> {t('common.movementReport')}{' '}
                            </TabsTrigger>
                            <TabsTrigger value="TripActivitiesReport">
                                {' '}
                                <MdOutlineTimeline className="text-blue-400" /> {t('common.activitiesReport')}{' '}
                            </TabsTrigger>
                            <TabsTrigger value="TripEventsReport">
                                {' '}
                                <TbTimelineEventText className="text-emerald-700" /> {t('common.eventsReport')}{' '}
                            </TabsTrigger>
                            <TabsTrigger value="TripViewer">
                                {' '}
                                <FaEye className="text-emerald-400" /> {t('common.tripViewer')}
                            </TabsTrigger>
                            <TabsTrigger value="AlertsViewer">
                                {' '}
                                <FcList /> {t('common.alertsViewer')}{' '}
                            </TabsTrigger>
                        </TabsList>

                        <TabsContent value="TripMap">
                            {' '}
                            <TripMap />{' '}
                        </TabsContent>
                        <TabsContent value="TripWarnings">
                            {' '}
                            <TripWarningsView />{' '}
                        </TabsContent>
                        <TabsContent value="TripPings">
                            {' '}
                            <TripPingsView />{' '}
                        </TabsContent>
                        <TabsContent value="MovementReport">
                            {' '}
                            <TripMovementReportView />{' '}
                        </TabsContent>
                        <TabsContent value="TripActivitiesReport">
                            {' '}
                            <TripActivitiesReportView />{' '}
                        </TabsContent>
                        <TabsContent value="TripEventsReport">
                            {' '}
                            <TripEventsReportView />{' '}
                        </TabsContent>
                        <TabsContent value="TripViewer">
                            {' '}
                            <TripViewersView />{' '}
                        </TabsContent>
                        <TabsContent value="AlertsViewer">
                            {' '}
                            <TripAlertsViewersView />{' '}
                        </TabsContent>
                    </div>
                </div>
            </Tabs>
        </section>
    );
}
