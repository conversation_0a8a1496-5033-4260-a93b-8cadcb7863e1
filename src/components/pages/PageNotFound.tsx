import { useTranslation } from 'react-i18next';
import { FaArrowLeftLong } from 'react-icons/fa6';
import { FcStatistics } from 'react-icons/fc';
import { useNavigate } from 'react-router-dom';

import NotFoundImage from '@imgs/404.avif';
import { Button } from '@/components/common/ui/Button';

export default function PageNotFound() {
    const { t } = useTranslation();
    const navigate = useNavigate();

    return (
        <section className="grid place-items-center mt-[2rem]">
            <div>
                <div className="max-w-[400px] mb-[1.5rem] mix-blend-multiply">
                    <img src={NotFoundImage} alt="404 Not Found" width="100%" />
                </div>

                <div className="text-center">
                    <h6 className="mb-8 text-3xl font-semibold"> {t('common.pageNotFound')} </h6>

                    <Button
                        variant="ghost"
                        className="w-40 border border-[#777] hover:text-[var(--blue-color)]"
                        onClick={() => window.history.back()}>
                        <FaArrowLeftLong />
                        {t('common.back')}
                    </Button>

                    <Button
                        variant="outline"
                        className="w-40 mx-4 hover:text-[var(--blue-color)]"
                        onClick={() => navigate('/dashboard')}>
                        <FcStatistics />
                        {t('navbar.dashboard')}
                    </Button>
                </div>
            </div>
        </section>
    );
}

// test
