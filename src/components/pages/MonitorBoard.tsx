import { GoogleMap } from '@/components/common/google-map/GoogleMap';
import { useMapPointLookups } from '@/stores/map-points.store';
import { usePortMapMarkers } from '@/stores/ports.store';
import { useTripLocationsLookups } from '@/stores/trip-location.store';

import MonitorMenu from '../features/monitor-board/MonitorMenu';

export default function MonitorBoard() {
    const mapPoints = useMapPointLookups();
    const ports = usePortMapMarkers();
    const tripLocations = useTripLocationsLookups();

    return (
        <GoogleMap points={mapPoints} ports={ports} tripLocations={tripLocations}>
            <MonitorMenu />
        </GoogleMap>
    );
}
