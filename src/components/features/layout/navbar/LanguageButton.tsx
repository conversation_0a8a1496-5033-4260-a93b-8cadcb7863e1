import { IoLanguage } from 'react-icons/io5';
import './Navbar.css';
import { useTranslation } from 'react-i18next';

import arabicLang from '@imgs/language/arabic.png';
import englishLang from '@imgs/language/english.png';
import { useChangeLanguage } from '@/stores/localization.store';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/common/ui/DropdownMenu';

export default function LanguageButton({ withLabel }: { withLabel?: boolean }) {
    const { t } = useTranslation();
    const changeLanguage = useChangeLanguage();
    const handleLanguageChange = (language: string) => {
        changeLanguage(language);
    };

    return (
        <>
            <DropdownMenu>
                <DropdownMenuTrigger className="flex items-center justify-between outline-none">
                    <div className="flex gap-3 items-center">
                        <IoLanguage className="m-auto size-5.5" />
                        {withLabel && t('navbar.language')}
                    </div>
                </DropdownMenuTrigger>

                <DropdownMenuContent side="bottom" className="text-center _effect">
                    <DropdownMenuItem
                        className="cursor-pointer flex gap-3 mb-1 justify-center"
                        onClick={() => handleLanguageChange('ar')}>
                        <img src={arabicLang} className="logo" alt="Arabic Language" width="20" />
                        العربية
                    </DropdownMenuItem>

                    <DropdownMenuItem
                        className="cursor-pointer flex gap-3 mb-2 justify-center"
                        onClick={() => handleLanguageChange('en')}>
                        <img src={englishLang} className="logo" alt="English Language" width="20" />
                        English
                    </DropdownMenuItem>
                </DropdownMenuContent>
            </DropdownMenu>
        </>
    );
}
