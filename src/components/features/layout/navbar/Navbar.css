.nav-items-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.nav-items-container .item {
    color: var(--main-font-color);
    text-shadow: 0 0 0 #000;
    font-size: 16px;
    cursor: pointer;
    padding: 6px 10px;
    transition: 0.3s;
    border-radius: 8px;
    white-space: nowrap;
    outline-color: transparent;
    margin-bottom: 2px;
}

.nav-items-container .item:hover,
.nav-items-container .item.active {
    background-color: var(--light-color);
    box-shadow: 1px 8px 36px #00000008;
}

/* Active child styling for reports dropdown items */
.report-item.active-child {
    background-color: var(--light-color);
    border: 1px solid var(--primary-color, #007bff);
    border-radius: 6px;
}

 