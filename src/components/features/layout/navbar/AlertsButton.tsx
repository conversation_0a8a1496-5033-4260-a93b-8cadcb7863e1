import './Navbar.css';
import { FcAdvertising } from 'react-icons/fc';
import { PopoverContent } from '@radix-ui/react-popover';

import { Popover, PopoverTrigger } from '@/components/common/ui/Popover';
import AlertsTap from '@/components/features/monitor-board/menu-taps/AlertsTap';

export default function AlertsButton() {
    return (
        <>
            <Popover>
                <PopoverTrigger asChild>
                    <button className="bg-gray-100 rounded-[50%] p-[8px] text-[22px]">
                        <FcAdvertising />
                    </button>
                </PopoverTrigger>
                <PopoverContent side="bottom" className="w-100 mt-2 z-50">
                    <AlertsTap />
                </PopoverContent>
            </Popover>
        </>
    );
}
