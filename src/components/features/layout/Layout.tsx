import { PrimeReactProvider } from 'primereact/api';
import { Outlet } from 'react-router-dom';

import Footer from './footer/Footer';
import Navbar from './navbar/Navbar';

function Layout() {
    return (
        <>
            <PrimeReactProvider>
                <main className="main-layout-container flex flex-col h-[100dvh]">
                    <div className="px-8">
                        <Navbar />
                    </div>

                    <div className="mx-[1rem] rounded-[8px] bg-[var(--light-color)] flex-grow-1 overflow-hidden">
                        <Outlet />
                    </div>

                    <Footer />
                </main>
            </PrimeReactProvider>
        </>
    );
}

export default Layout;
