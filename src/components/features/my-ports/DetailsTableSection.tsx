import { useTranslation } from 'react-i18next';
import { FaMapMarkedAlt } from 'react-icons/fa';
import { FaLocationCrosshairs } from 'react-icons/fa6';
import { FcAdvertising, FcComboChart } from 'react-icons/fc';
import { GoMail } from 'react-icons/go';
import { Link } from 'react-router-dom';

import Tooltip from '@/components/common/ui/Tooltip';
import LabelingItem, { type ILabelingItem } from '@/components/common/labeling-item/LabelingItem';
import EmailDialog from '@/components/common/ui/EmailDialog';

export default function DetailsTableSection() {
    const { t } = useTranslation();

    // Just need to update 'data' property by actual data
    const tableData: ILabelingItem[] = [
        { label: t('common.tripCode'), data: '1949389922' },
        { label: t('common.transitSequenceNo'), data: '587' },
        { label: t('common.owner'), data: 'Owner Name' },
        { label: t('common.elocks'), data: 'V123' },
        {
            label: t('common.tracker'),
            data: 'ModelX, Red, TypeY, PlateCountryName',
        },
        {
            label: t('common.vehicleDetails'),
            data: 'A3C1215596999, ModelX, Red, TypeY, PlateCountryName',
            dataInNewLine: true,
        },
        {
            label: t('common.shipmentDescription'),
            data: 'ModelX, Red, TypeY, PlateCountryName',
            dataInNewLine: true,
        },
        { label: t('common.completeDistance'), data: '00 KM' },
        { label: t('common.remainingDistance'), data: '00 KM' },
        { label: t('common.driverInfo'), data: '-' },
        { label: t('common.expectedArrivalDate'), data: '-' },
        { label: t('common.endDate'), data: '' },
        { label: t('common.securityNotes'), data: '-' },
    ];

    return (
        <>
            <div className="px-5 h-full overflow-y-auto _scrollable">
                <div className="trip-details-table">
                    <div className="flex flex-wrap gap-3 justify-between items-center mb-2">
                        <p className="text-red-500 text-[18px] text-center font-medium"> {t('common.tripDetails')} </p>
                        <span className="flex gap-3">
                            <Tooltip tooltipMessage="common.tripMap">
                                <Link to="/trip-information">
                                    <FaMapMarkedAlt className="size-5 cursor-pointer text-red-600" />
                                </Link>
                            </Tooltip>

                            <Tooltip tooltipMessage="common.tripAlerts">
                                <FcAdvertising className="size-5 cursor-pointer" />
                            </Tooltip>

                            <Tooltip tooltipMessage="tooltips.email">
                                <EmailDialog>
                                    <span className="inline-block">
                                        <GoMail className="size-5 cursor-pointer" />
                                    </span>
                                </EmailDialog>
                            </Tooltip>

                            <Tooltip tooltipMessage="common.tripPings">
                                <FaLocationCrosshairs className="size-4.5 text-green-600 cursor-pointer" />
                            </Tooltip>

                            <Tooltip tooltipMessage="common.movementReport">
                                <FcComboChart className="size-5 cursor-pointer" />
                            </Tooltip>
                        </span>
                    </div>
                </div>

                <div>
                    {tableData.map((item, index) => (
                        <div key={index} className="even:bg-[#aaaaaa20]">
                            <LabelingItem {...item} />
                        </div>
                    ))}
                </div>

                <hr className="mt-3 my-2" />

                <div className="trip-details-table">
                    <h5 className="text-red-500 text-[18px] text-center font-medium mb-2">
                        {' '}
                        {t('common.alertsDetails')}{' '}
                    </h5>
                    <p className="text-gray-400 text-center text-[14px]"> No Data Exist </p>
                </div>
            </div>
        </>
    );
}
