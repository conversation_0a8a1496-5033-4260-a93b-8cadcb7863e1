import { LucideFullscreen } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { CiViewList } from 'react-icons/ci';
import { useState } from 'react';

import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
} from '@/components/common/ui/Dialog';
import Tooltip from '@/components/common/ui/Tooltip';
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/common/ui/Resizable';

import MapSection from './MapSection';
import DetailsTableSection from './DetailsTableSection';

export default function MyPortsDetails() {
    const { t } = useTranslation();
    const [isOpen, setIsOpen] = useState(false);

    return (
        <>
            <section className="my-ports-details-container">
                <Dialog open={isOpen} onOpenChange={setIsOpen}>
                    <DialogTrigger asChild>
                        <Tooltip tooltipMessage="common.viewDetails">
                            <span className="cursor-pointer inline-block">
                                <LucideFullscreen
                                    className="size-5 text-blue-600 min-w-fit m-auto"
                                    onClick={() => setIsOpen(true)}
                                />
                            </span>
                        </Tooltip>
                    </DialogTrigger>

                    <DialogDescription></DialogDescription>

                    <DialogContent className="grid-rows-[53px_1fr] w-screen h-screen max-w-none max-h-none p-0 [&>button>svg]:h-8 [&>button>svg]:w-6">
                        <DialogHeader dir="ltr" className="px-6 py-3 max-h-[100px] flex items-start">
                            <DialogTitle>
                                {' '}
                                <span className="text-2xl text-[20px] text-blue-600 flex items-center gap-3 mt-1">
                                    {' '}
                                    <CiViewList className="size-6" /> {t('common.tripDetails')}{' '}
                                </span>{' '}
                            </DialogTitle>
                        </DialogHeader>

                        <div className="flex flex-grow-1 gap-2 h-full pt-0">
                            <ResizablePanelGroup direction="horizontal" className="h-full">
                                <ResizablePanel defaultSize={25}>
                                    <DetailsTableSection />
                                </ResizablePanel>

                                <ResizableHandle withHandle />

                                <ResizablePanel>
                                    <MapSection />
                                </ResizablePanel>
                            </ResizablePanelGroup>
                        </div>
                    </DialogContent>
                </Dialog>
            </section>
        </>
    );
}
