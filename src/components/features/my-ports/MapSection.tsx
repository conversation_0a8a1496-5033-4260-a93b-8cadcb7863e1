import { IoChevronUpOutline } from 'react-icons/io5';
import { FcCombo<PERSON>hart } from 'react-icons/fc';
import { useTranslation } from 'react-i18next';

import LineChart from '@/components/common/ui/LineChart';
import type { ChartConfig } from '@/components/common/ui/Chart';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/common/ui/Tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/common/ui/Card';
import { GoogleMap } from '@/components/common/google-map/GoogleMap';

export default function MapSection() {
    const { t } = useTranslation();

    // Dummy Data
    const chartData = [
        { month: 'January', active: 186, inActive: 80 },
        { month: 'February', active: 305, inActive: 200 },
        { month: 'March', active: 237, inActive: 120 },
        { month: 'April', active: 73, inActive: 190 },
        { month: 'May', active: 209, inActive: 130 },
        { month: 'June', active: 214, inActive: 140 },
    ];

    const chartConfig = {
        active: {
            label: 'Active',
            color: '#2563eb',
        },
        inActive: {
            label: 'inActive',
            color: '#60a5fa',
        },
    } satisfies ChartConfig;

    return (
        <>
            <div className="relative w-full h-full">
                <Tabs defaultValue="Chart" className="absolute top-[1rem] right-[1rem] z-10 me-3">
                    <TabsList className="ms-auto">
                        <TabsTrigger value="Chart"> {t('common.chart')} </TabsTrigger>
                        <TabsTrigger value="close">
                            <IoChevronUpOutline className="size-3.2" />
                        </TabsTrigger>
                    </TabsList>

                    <TabsContent value="Chart">
                        <Card className="py-5 px-0">
                            <CardContent className="max-h[70vh] px-2">
                                <CardHeader className="p-0">
                                    <CardTitle className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                            <FcComboChart className="size-5 text-[#aaa]" />
                                            {t('common.chart')}
                                        </div>
                                    </CardTitle>
                                </CardHeader>
                                <div className="w-[320px] mt-4">
                                    <LineChart chartConfig={chartConfig} chartData={chartData} dataKey="month" />
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>
                    <TabsContent value="close"></TabsContent>
                </Tabs>

                <GoogleMap />
            </div>
        </>
    );
}
