'use client';

import { useTranslation } from 'react-i18next';
import { Route } from 'lucide-react';
import { useEffect, useCallback } from 'react';

import { Card, CardContent } from '@/components/common/ui/Card';
import { Radio } from '@/components/common/ui/Radio';
import { Checkbox } from '@/components/common/ui/Checkbox';
import { useTripFiltersStore } from '@/stores/trip-filters.store';
import { useTripLocationStore } from '@/stores/trip-location.store';
import { Icon } from '@/components/common/ui/Icon';
import type { TripLocationRequest } from '@/infrastructure/api/trips/types';
import { TripCategory, TripLocation } from '@/shared/enums';

const TRIP_CATEGORY_OPTIONS = [
    { key: TripCategory.ALL_TRIPS, label: 'allTrips' },
    { key: TripCategory.MY_PORTS, label: 'myRoutes' },
    { key: TripCategory.SUSPICIOUS_TRIPS, label: 'suspiciousTrips' },
    { key: TripCategory.FOCUSED_TRIPS, label: 'focusedTrips' },
    { key: TripCategory.STOPPED_TRIPS, label: 'stoppedTrips' },
] as const;

const TRIP_STATUS_OPTIONS = [
    { label: 'insideEntryPort', value: TripLocation.ENTRY },
    { label: 'onRoute', value: TripLocation.ON_ROUTE },
    { label: 'inExitPort', value: TripLocation.EXIT },
] as const;

interface LocalProps {
    onFilterChange: () => void;
}
export default function RoutesTap({ onFilterChange }: LocalProps) {
    const { t, i18n } = useTranslation();
    const { filters, setFilter, updateListSection } = useTripFiltersStore();
    const { loadTripLocations } = useTripLocationStore();

    useEffect(() => {
        onFilterChange();
    }, [filters.tripCategory, filters.tripLocations, onFilterChange]);

    // Handle Select All functionality
    const handleSelectAll = useCallback(async () => {
        // Check if all options are currently selected
        const allSelected = TRIP_STATUS_OPTIONS.every(({ value }) => filters.tripLocations.includes(value));

        if (allSelected) {
            // Deselect all
            TRIP_STATUS_OPTIONS.forEach(({ value }) => {
                updateListSection('tripLocations', value, false);
            });
        } else {
            // Select all
            TRIP_STATUS_OPTIONS.forEach(({ value }) => {
                updateListSection('tripLocations', value, true);
            });
        }

        // Trigger API call immediately with updated filters
        const updatedFilters = {
            ...filters,
            tripLocations: allSelected ? [] : TRIP_STATUS_OPTIONS.map(({ value }) => value),
        };

        await loadTripLocations(updatedFilters as TripLocationRequest);
    }, [filters, updateListSection, loadTripLocations]);

    return (
        <Card className="_effect py-2" dir={i18n.dir()}>
            <CardContent className="p-0">
                {/* Trip Category */}
                <div className="border-b border-gray-200 last:border-b-0 space-y-2 px-4 py-3">
                    <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
                        <Route className="w-4 h-4 text-green-700" />
                        <span className="text-lg font-bold">{t('filter.tripCategory')}</span>
                    </div>
                    <div className="flex flex-col gap-0.5">
                        {TRIP_CATEGORY_OPTIONS.map(({ key, label }) => (
                            <Radio
                                key={key}
                                name="tripCategory"
                                label={t(`filter.${label}`)}
                                checked={filters.tripCategory === key}
                                onChange={() => {
                                    setFilter('tripCategory', key);
                                }}
                            />
                        ))}
                    </div>
                </div>

                {/* Trip Status — always enabled */}
                <div className=" space-y-2 px-4 py-3">
                    <div className="flex items-center justify-between">
                        {/* Left side: icon + text */}
                        <div className="flex items-center gap-2">
                            <Icon name="shipmentTracking" />
                            <span className="text-lg font-bold">{t('filter.tripLocation')}</span>
                        </div>

                        {/* Right side: button */}
                        <button
                            className="text-green-700 text-sm font-bold py-1 hover:text-green-800 transition-colors"
                            onClick={handleSelectAll}>
                            {TRIP_STATUS_OPTIONS.every(({ value }) => filters.tripLocations.includes(value))
                                ? i18n.language === 'ar'
                                    ? 'إلغاء تحديد الكل'
                                    : 'Deselect All'
                                : t('filter.selectAll')}
                        </button>
                    </div>

                    {/* Options */}
                    <div className="flex flex-col gap-0.5">
                        {TRIP_STATUS_OPTIONS.map(({ label, value }) => (
                            <Checkbox
                                key={value}
                                label={t(`filter.${label}`)}
                                checked={filters.tripLocations.includes(value)}
                                onChange={async (checked: boolean) => {
                                    updateListSection('tripLocations', value, checked);

                                    // Trigger API call immediately with updated filters
                                    const updatedTripLocations = checked
                                        ? [...filters.tripLocations, value]
                                        : filters.tripLocations.filter((loc) => loc !== value);

                                    const updatedFilters = {
                                        ...filters,
                                        tripLocations: updatedTripLocations,
                                    };

                                    await loadTripLocations(updatedFilters as TripLocationRequest);
                                }}
                            />
                        ))}
                    </div>
                </div>
            </CardContent>
        </Card>
    );
}
