export type TripDisplayMode = 'cluster' | 'individual';

export interface DisplaySettings {
    tripDisplayMode: TripDisplayMode;
    checkpoints: {
        customs: boolean;
        police: boolean;
        suspiciousGeofences: boolean;
    };
    ports: {
        land: boolean;
        sea: boolean;
        air: boolean;
    };
}

export type CheckpointType = keyof DisplaySettings['checkpoints'];
export type PortType = keyof DisplaySettings['ports'];

export interface DisplaySettingsState {
    settings: DisplaySettings;
}

export interface DisplaySettingsActions {
    setTripDisplayMode: (mode: TripDisplayMode) => void;
    toggleCheckpoint: (type: CheckpointType) => void;
    togglePort: (type: PortType) => void;
}

export type DisplaySettingsStoreType = DisplaySettingsState & DisplaySettingsActions;

// Option definition for rendering selection controls
export interface Option<K> {
    key: K;
    label: string;
    default?: boolean;
}
