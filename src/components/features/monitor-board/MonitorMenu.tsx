import { useCallback, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { IoChevronUpOutline } from 'react-icons/io5';

import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/common/ui/Tabs';
import { Button } from '@/components/common/ui/Button';
import TripFilterDialog from '@/components/common/trip-filter-dialog/TripFilterDialog';
import { useAlertStore } from '@/stores/alert.store';
import { useMapPointsStore } from '@/stores/map-points.store';
import { useDisplaySettingsStore } from '@/stores/display-settings.store';
import { usePortsStore } from '@/stores/ports.store';
import { useTripLocationStore } from '@/stores/trip-location.store';
import { useTripFiltersStore } from '@/stores/trip-filters.store';
import { useUIStore } from '@/stores/ui.store';
import type { TripLocationRequest } from '@/infrastructure/api/trips/types';
import { MapPointEntryType, PortType } from '@/shared/enums';

import RoutesTap from './menu-taps/RoutesTap';
import AlertsTap from './menu-taps/AlertsTap';
import DisplaySettingsTap from './menu-taps/display-settings-tap/DisplaySettingsTap';

type MonitorMenuProps = {
    mapDialogRootRef?: React.RefObject<HTMLElement | null>;
};

export default function MonitorMenu({ mapDialogRootRef }: MonitorMenuProps) {
    const { t } = useTranslation();
    const [visible, setVisible] = useState(false);
    const listenToAlerts = useAlertStore((state) => state.listenToAlerts);
    const loadMapPoints = useMapPointsStore((state) => state.loadMapPoints);
    const loadPorts = usePortsStore((state) => state.loadPorts);
    const loadTripLocations = useTripLocationStore((state) => state.loadTripLocations);
    const filters = useTripFiltersStore((state) => state.filters);

    // UI store state & actions
    const activeTab = useUIStore((s) => s.activeTab);
    const setActiveTab = useUIStore((s) => s.setActiveTab);

    // map tripCategory key -> translation key
    const tripCategoryLabels: Record<number, string> = {
        1: 'filter.allTrips',
        2: 'filter.myRoutes',
        3: 'filter.suspiciousTrips',
        4: 'filter.focusedTrips',
        5: 'filter.stoppedTrips',
    };

    // fallback: if no category selected, show default
    const selectedCategoryTitle =
        filters.tripCategory && tripCategoryLabels[filters.tripCategory]
            ? t(tripCategoryLabels[filters.tripCategory])
            : t('common.routes');

    useEffect(() => {
        listenToAlerts();
    }, [listenToAlerts]);

    // todo: trigger when loading user filters from cache
    const settings = useDisplaySettingsStore((state) => state.settings);
    useEffect(() => {
        loadMapPoints({
            pageSize: 1000,
            pageNumber: 1,
            entryTypes: [
                settings.checkpoints.police ? MapPointEntryType.POLICE_STATION : null,
                settings.checkpoints.customs ? MapPointEntryType.CHECKPOINT : null,
                settings.checkpoints.suspiciousGeofences ? MapPointEntryType.SUSPECTED_AREA : null,
                MapPointEntryType.NONE,
            ].filter((x) => x != null) as MapPointEntryType[],
        });
        loadPorts({
            pageSize: 1000,
            pageNumber: 1,
            types: [
                settings.ports.land ? PortType.LAND_PORT : null,
                settings.ports.sea ? PortType.SEA_PORT : null,
                settings.ports.air ? PortType.AIR_PORT : null,
                PortType.NONE,
            ].filter((x) => x != null) as PortType[],
        });
        loadTripLocations({
            pageSize: 1000,
        });
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    useEffect(() => {
        // Subscribe to display settings changes - only when settings actually change
        const unsubscribeDisplay = useDisplaySettingsStore.subscribe(
            (state) => state.settings,
            (settings) => {
                loadMapPoints({
                    pageSize: 1000,
                    pageNumber: 1,
                    entryTypes: [
                        settings.checkpoints.police ? MapPointEntryType.POLICE_STATION : null,
                        settings.checkpoints.customs ? MapPointEntryType.CHECKPOINT : null,
                        settings.checkpoints.suspiciousGeofences ? MapPointEntryType.SUSPECTED_AREA : null,
                        MapPointEntryType.NONE,
                    ].filter((x) => x != null) as MapPointEntryType[],
                });
                loadPorts({
                    pageSize: 1000,
                    pageNumber: 1,
                    types: [
                        settings.ports.land ? PortType.LAND_PORT : null,
                        settings.ports.sea ? PortType.SEA_PORT : null,
                        settings.ports.air ? PortType.AIR_PORT : null,
                        PortType.NONE,
                    ].filter((x) => x != null) as PortType[],
                });
            },
        );

        // Cleanup function to unsubscribe when component unmounts
        return () => {
            unsubscribeDisplay();
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []); // Empty dependency array - only subscribe once on mount

    const applyFilter = useCallback(() => {
        loadTripLocations(filters as TripLocationRequest);
    }, [loadTripLocations, filters]);

    // Controlled Tabs value is activeTab from uiStore. Tabs component expects string value.
    const tabsValue = activeTab ?? ''; // empty string when no active tab

    const onTabsValueChange = useCallback(
        (value: string) => {
            // If clicking the "Search" trigger we also open TripFilterDialog
            if (value === 'Search') {
                // set store only if different
                if (activeTab !== 'Search') setActiveTab('Search');
                setVisible(true);
                return;
            }

            // 'close' special action -> close tabs
            if (value === 'close') {
                if (activeTab !== null) setActiveTab(null);
                return;
            }

            // other tabs: set active tab (or null)
            if (value) {
                if (activeTab !== value) setActiveTab(value);
            } else {
                if (activeTab !== null) setActiveTab(null);
            }
        },
        [activeTab, setActiveTab],
    );

    return (
        <Tabs className={`absolute top-2.5 right-15`} value={tabsValue} onValueChange={onTabsValueChange}>
            <TabsList className="m-auto h-10 bg-white [&_[data-state=active]]:bg-gray-100">
                <TabsTrigger value="DisplaySettings">{t('common.display_settings_button')}</TabsTrigger>
                <TabsTrigger value="Search">
                    <Button
                        size="sm"
                        className="w-full"
                        onClick={() => {
                            if (activeTab !== 'Search') setActiveTab('Search');
                            setVisible(true);
                        }}>
                        {t('common.search')}
                    </Button>
                    <TripFilterDialog
                        onApply={applyFilter}
                        open={visible}
                        onOpenChange={(open) => {
                            setVisible(open);
                        }}
                        portalContainer={mapDialogRootRef?.current ?? undefined}
                        showDate={false}
                        showSorting={false}
                        showTripStatus={false}
                    />
                </TabsTrigger>
                <TabsTrigger value="Alerts">{t('reports.alerts')}</TabsTrigger>
                <TabsTrigger value="Routes">{selectedCategoryTitle}</TabsTrigger>
                <TabsTrigger value="close">
                    <IoChevronUpOutline className="size-3.2" />
                </TabsTrigger>
            </TabsList>

            <TabsContent value="DisplaySettings" className="max-h-[70vh] overflow-x-hidden overflow-y-auto">
                <DisplaySettingsTap />
            </TabsContent>
            <TabsContent value="Alerts" className="max-h-[70vh] overflow-x-hidden overflow-y-auto">
                <AlertsTap />
            </TabsContent>
            <TabsContent value="Routes" className="max-h-[70vh] overflow-x-hidden overflow-y-auto">
                <RoutesTap onFilterChange={applyFilter} />
            </TabsContent>
        </Tabs>
    );
}
