import { FaRoute } from 'react-icons/fa';
import { SlNote } from 'react-icons/sl';
import { TbMapCheck } from 'react-icons/tb';
import { useTranslation } from 'react-i18next';
import { GoMail } from 'react-icons/go';

import LabelingItem, { type ILabelingItem } from '@/components/common/labeling-item/LabelingItem';
import Tooltip from '@/components/common/ui/Tooltip';
import EmailDialog from '@/components/common/ui/EmailDialog';

import { useSelectedTripStore } from '../../../stores/selected-trip.store';

export default function TripDetails() {
    const { t } = useTranslation();
    const selectedTrip = useSelectedTripStore((state) => state.selectedTrip);

    // Just need to update 'data' property by actual data
    const tableData: ILabelingItem[] = [
        {
            label: t('tripDetails.transitNumber'),
            data: selectedTrip?.transitNumber || '-',
        },
        {
            label: t('tripDetails.transitType'),
            data: selectedTrip?.transitType || '-',
        },
        {
            label: t('tripDetails.declarationDate'),
            data: selectedTrip?.declarationDate || '-',
        },
        {
            label: t('tripDetails.transitSeqNo'),
            data: selectedTrip?.transitSeqNo || '-',
        },
        {
            label: t('tripDetails.ownerDescription'),
            data: selectedTrip?.ownerDescription || '-',
        },
        { label: t('tripDetails.entryPort'), data: selectedTrip?.entryPort || '-' },
        { label: t('tripDetails.exitPort'), data: selectedTrip?.exitPort || '-' },
        {
            label: t('tripDetails.startingDate'),
            data: selectedTrip?.startingDate || '-',
        },
        {
            label: t('tripDetails.expectedArrivalDate'),
            data: selectedTrip?.expectedArrivalDate || '-',
        },
        { label: t('tripDetails.endDate'), data: selectedTrip?.endDate || '-' },
        { label: t('tripDetails.trackerNo'), data: selectedTrip?.trackerNo || '-' },
        { label: t('tripDetails.elocks'), data: selectedTrip?.elocks || '-' },
        {
            label: t('tripDetails.shipmentDescription'),
            data: selectedTrip?.shipmentDescription || '-',
            dataInNewLine: true,
        },
        {
            label: t('tripDetails.vehicleDetails'),
            data: selectedTrip?.vehicleDetails || '-',
            dataInNewLine: true,
        },
        {
            label: t('tripDetails.driverName'),
            data: selectedTrip?.driverName || '-',
        },
        {
            label: t('tripDetails.driverPassportNumber'),
            data: selectedTrip?.driverPassportNumber || '-',
        },
        {
            label: t('tripDetails.driverNationality'),
            data: selectedTrip?.driverNationality || '-',
        },
        {
            label: t('tripDetails.driverContactNo'),
            data: selectedTrip?.driverContactNo || '-',
        },
        {
            label: t('tripDetails.securityNotes'),
            data: selectedTrip?.securityNotes || '-',
        },
        {
            label: t('tripDetails.completeDistance'),
            data: selectedTrip?.completeDistance || '-',
        },
        {
            label: t('tripDetails.remainingDistance'),
            data: selectedTrip?.remainingDistance || '-',
        },
        { label: t('tripDetails.status'), data: selectedTrip?.status || '-' },
    ];

    return (
        <div className="overflow-y-auto h-full px-[10px] py-[5px] _scrollable">
            <div className="flex justify-between items-center mb-2 rtl:flex-row-reverse">
                <p className="text-red-500 text-[18px] text-center font-medium"> {t('common.tripDetails')} </p>
                <span className="flex gap-4">
                    <Tooltip tooltipMessage="tripDetails.tripTracking">
                        <FaRoute className="size-5 cursor-pointer text-blue-500" />
                    </Tooltip>

                    <Tooltip tooltipMessage="tripDetails.endingTrip">
                        <TbMapCheck className="size-6 cursor-pointer text-red-600" />
                    </Tooltip>

                    <Tooltip tooltipMessage="tooltips.email">
                        <EmailDialog>
                            <span>
                                <GoMail className="size-5 cursor-pointer" />
                            </span>
                        </EmailDialog>
                    </Tooltip>

                    <Tooltip tooltipMessage="tripDetails.addSecurityNotes">
                        <SlNote className="size-5 cursor-pointer text-green-800" />
                    </Tooltip>
                </span>
            </div>

            <div>
                {tableData.map((item, index) => (
                    <div key={index} className="even:bg-[#aaaaaa20]">
                        <LabelingItem {...item} />
                    </div>
                ))}
            </div>
        </div>
    );
}
