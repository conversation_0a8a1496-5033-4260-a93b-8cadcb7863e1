import { appConfig } from '@/shared/config/app-settings.config';
import { SignalrHubManager } from '@/shared/lib/SignalR/signalrHubManager';
import { SubscriptionManager } from '@/shared/lib/SignalR/subscriptionManager';
import type { ConnectionState, SubscriptionId } from '@/shared/lib/SignalR/types';

import { logger } from '../../logging';

import type { AlertMessage, TripUpdateMessage } from './types';

export type TripHubEvents = {
    ReceiveAllTripAlerts: AlertMessage;
    ReceiveTripAlert: AlertMessage;
    ReceiveGeneralEvent: TripUpdateMessage;
    ConnectionStateChanged: ConnectionState;
};

export class TripHub {
    public static instance: TripHub;
    private hubManager: SignalrHubManager;
    private subManager: SubscriptionManager<TripHubEvents>;

    private constructor() {
        TripHub.instance = this;
        this.subManager = new SubscriptionManager<TripHubEvents>();

        const hubUrl = `${appConfig.get('hubBaseUrl')}/hub/trips`;

        this.hubManager = new SignalrHubManager({
            hubUrl,
            onConnectionStateChange: (state: ConnectionState) => {
                this.subManager.broadcast('ConnectionStateChanged', state);
            },
        });
    }

    public static getInstance(): TripHub {
        if (!TripHub.instance) {
            TripHub.instance = new TripHub();
        }
        return TripHub.instance;
    }

    // ================================ Connect ================================

    public async connect(): Promise<void> {
        try {
            await this.hubManager.connect((hub) => {
                hub.on('ReceiveAllTripAlerts', (alert: AlertMessage) =>
                    this.subManager.broadcast('ReceiveAllTripAlerts', alert),
                );
                hub.on('ReceiveTripAlert', (alert: AlertMessage) =>
                    this.subManager.broadcast('ReceiveTripAlert', alert),
                );
                hub.on('ReceiveGeneralEvent', (event: TripUpdateMessage) =>
                    this.subManager.broadcast('ReceiveGeneralEvent', event),
                );
            });
        } catch (error) {
            logger.error('TripHub: Connection failed', error as Error);
            throw error;
        }
    }

    // ================================ Disconnect ================================

    public disconnect(): void {
        this.hubManager.disconnect();
    }

    // ================================ Subscriptions ================================

    public subscribe<K extends keyof TripHubEvents>(
        eventName: K,
        callback: (data: TripHubEvents[K]) => void,
    ): SubscriptionId {
        return this.subManager.subscribe(eventName, callback);
    }

    public unsubscribe<K extends keyof TripHubEvents>(eventName: K, subscriberId: SubscriptionId): boolean {
        return this.subManager.unsubscribe(eventName, subscriberId);
    }

    // ================================ Groups ================================
    public joinAllTripsGroup(): Promise<unknown> {
        return this.hubManager.sendToHub('SubscribeToAllTripAlerts');
    }

    public leaveAllTripsGroup(): Promise<unknown> {
        return this.hubManager.sendToHub('UnsubscribeFromAllTripAlerts');
    }

    public joinGeneralEventsGroup(): Promise<unknown> {
        return this.hubManager.sendToHub('SubscribeToGeneralEvents');
    }

    public leaveGeneralEventsGroup(): Promise<unknown> {
        return this.hubManager.sendToHub('UnsubscribeFromGeneralEvents');
    }

    public joinTripGroup(tripId: string): Promise<unknown> {
        return this.hubManager.sendToHub('SubscribeToTripAlerts', tripId);
    }

    public leaveTripGroup(tripId: string): Promise<unknown> {
        return this.hubManager.sendToHub('UnsubscribeFromTripAlerts', tripId);
    }

    // ================================ Dispose ================================
    public dispose(): void {
        this.disconnect();
        this.subManager.dispose();
        logger.info('TripHub: Disposed all resources');
    }
}

export const tripHub = TripHub.getInstance();
