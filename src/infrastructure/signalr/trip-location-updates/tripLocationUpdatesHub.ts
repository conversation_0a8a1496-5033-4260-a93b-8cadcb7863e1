import { appConfig } from '@/shared/config/app-settings.config';
import { SignalrHubManager } from '@/shared/lib/SignalR/signalrHubManager';
import { SubscriptionManager } from '@/shared/lib/SignalR/subscriptionManager';
import type { ConnectionState, SubscriptionId } from '@/shared/lib/SignalR/types';
import { logger } from '@/infrastructure/logging';

import type { LocationUpdateMessage } from './types';

export type TripLocationUpdatesHubEvents = {
    ReceiveAllTripsLocation: LocationUpdateMessage;
    ReceiveTripLocation: LocationUpdateMessage;
    ConnectionStateChanged: ConnectionState;
};

export class TripLocationUpdatesHub {
    public static instance: TripLocationUpdatesHub;
    private hubManager: SignalrHubManager;
    private subManager: SubscriptionManager<TripLocationUpdatesHubEvents>;

    private constructor() {
        TripLocationUpdatesHub.instance = this;
        this.subManager = new SubscriptionManager<TripLocationUpdatesHubEvents>();

        const hubUrl = `${appConfig.get('hubBaseUrl')}/hub/trips`;

        this.hubManager = new SignalrHubManager({
            hubUrl,
            onConnectionStateChange: (state: ConnectionState) => {
                this.subManager.broadcast('ConnectionStateChanged', state);
            },
        });
    }

    public static getInstance(): TripLocationUpdatesHub {
        if (!TripLocationUpdatesHub.instance) {
            TripLocationUpdatesHub.instance = new TripLocationUpdatesHub();
        }
        return TripLocationUpdatesHub.instance;
    }

    // ================================ Connect ================================

    public async connect(): Promise<void> {
        try {
            await this.hubManager.connect((hub) => {
                hub.on('ReceiveAllTripsLocation', (location: LocationUpdateMessage) =>
                    this.subManager.broadcast('ReceiveAllTripsLocation', location),
                );
                hub.on('ReceiveTripLocation', (location: LocationUpdateMessage) =>
                    this.subManager.broadcast('ReceiveTripLocation', location),
                );
            });
        } catch (error) {
            logger.error('TripLocationUpdatesHub: Connection failed', error as Error);
            throw error;
        }
    }

    // ================================ Disconnect ================================

    public disconnect(): void {
        this.hubManager.disconnect();
    }

    // ================================ Subscriptions ================================

    public subscribe<K extends keyof TripLocationUpdatesHubEvents>(
        eventName: K,
        callback: (data: TripLocationUpdatesHubEvents[K]) => void,
    ): SubscriptionId {
        return this.subManager.subscribe(eventName, callback);
    }

    public unsubscribe<K extends keyof TripLocationUpdatesHubEvents>(
        eventName: K,
        subscriberId: SubscriptionId,
    ): boolean {
        return this.subManager.unsubscribe(eventName, subscriberId);
    }

    // ================================ Groups ================================
    public joinAllTripsGroup(): Promise<unknown> {
        return this.hubManager.sendToHub('SubscribeToAllTrips');
    }

    public leaveAllTripsGroup(): Promise<unknown> {
        return this.hubManager.sendToHub('UnsubscribeFromAllTrips');
    }

    public joinTripGroup(tripId: string): Promise<unknown> {
        return this.hubManager.sendToHub('SubscribeToTrip', tripId);
    }

    public leaveTripGroup(tripId: string): Promise<unknown> {
        return this.hubManager.sendToHub('UnsubscribeFromTrip', tripId);
    }

    // ================================ Dispose ================================
    public dispose(): void {
        this.disconnect();
        this.subManager.dispose();
        logger.info('TripLocationUpdatesHub: Disposed all resources');
    }
}

export const tripLocationUpdatesHub = TripLocationUpdatesHub.getInstance();
tripLocationUpdatesHub.connect();
