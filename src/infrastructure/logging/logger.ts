/* eslint-disable no-console */
import type { <PERSON>ogger, LogLevel, LogEntry } from './logger.interface';

// Helper function to detect development mode
const isDevelopmentMode = (): boolean => {
    try {
        return (
            (window as Window)?.location?.hostname === 'localhost' ||
            (window as Window)?.location?.hostname === '127.0.0.1' ||
            (window as Window)?.location?.port === '3000'
        );
    } catch {
        return false;
    }
};

// Helper function for string padding
const padString = (str: string, length: number): string => {
    return str + ' '.repeat(Math.max(0, length - str.length));
};

export class Logger implements ILogger {
    private static instance: Logger;
    private logLevel: LogLevel = 'info';
    private isDevelopment: boolean = isDevelopmentMode();
    private logHistory: LogEntry[] = [];
    private maxHistorySize: number = 1000;

    private constructor() {
        // Set log level based on environment
        this.logLevel = this.isDevelopment ? 'debug' : 'warn';
    }

    public static getInstance(): Logger {
        if (!Logger.instance) {
            Logger.instance = new Logger();
        }
        return Logger.instance;
    }

    private formatMessage(level: LogLevel, message: string, context?: Record<string, unknown>, error?: Error): string {
        const timestamp = new Date().toISOString();
        const levelUpper = padString(level.toUpperCase(), 5);
        const contextStr = context ? ` | Context: ${JSON.stringify(context)}` : '';
        const errorStr = error ? ` | Error: ${error.message}` : '';

        return `[${timestamp}] ${levelUpper} | ${message}${contextStr}${errorStr}`;
    }

    private shouldLog(level: LogLevel): boolean {
        const levels: Record<LogLevel, number> = {
            debug: 0,
            info: 1,
            warn: 2,
            error: 3,
        };

        return levels[level] >= levels[this.logLevel];
    }

    private addToHistory(entry: LogEntry): void {
        this.logHistory.push(entry);

        if (this.logHistory.length > this.maxHistorySize) {
            this.logHistory = this.logHistory.slice(-this.maxHistorySize);
        }
    }

    private log(level: LogLevel, message: string, context?: Record<string, unknown>, error?: Error): void {
        if (!this.shouldLog(level)) {
            return;
        }

        const entry: LogEntry = {
            level,
            message,
            timestamp: new Date(),
            context,
            error,
        };

        this.addToHistory(entry);

        const formattedMessage = this.formatMessage(level, message, context, error);

        // Console output with colors in development
        if (this.isDevelopment) {
            const colors: Record<LogLevel, string> = {
                debug: '\x1b[36m', // Cyan
                info: '\x1b[32m', // Green
                warn: '\x1b[33m', // Yellow
                error: '\x1b[31m', // Red
            };
            const reset = '\x1b[0m';

            console.log(`${colors[level]}${formattedMessage}${reset}`);

            if (error && error.stack) {
                console.log(`${colors[level]}Stack trace: ${error.stack}${reset}`);
            }
        } else {
            // Production logging
            if (level === 'error') {
                console.error(formattedMessage);
            } else if (level === 'warn') {
                console.warn(formattedMessage);
            } else {
                console.log(formattedMessage);
            }
        }

        // Send to external logging service in production
        if (!this.isDevelopment && (level === 'error' || level === 'warn')) {
            this.sendToExternalLogger(entry);
        }
    }

    private async sendToExternalLogger(entry: LogEntry): Promise<void> {
        try {
            // In a real application, you would send this to services like:
            // - Sentry
            // - LogRocket
            // - DataDog
            // - CloudWatch
            // For demo purposes, we'll just store it

            // Example implementation:
            // await fetch('/api/logs', {
            //   method: 'POST',
            //   headers: { 'Content-Type': 'application/json' },
            //   body: JSON.stringify(entry)
            // });

            console.log('📤 Log sent to external service:', entry);
        } catch (error) {
            console.error('Failed to send log to external service:', error);
        }
    }

    // Public logging methods
    public debug(message: string, context?: Record<string, unknown>): void {
        this.log('debug', message, context);
    }

    public info(message: string, context?: Record<string, unknown>): void {
        this.log('info', message, context);
    }

    public warn(message: string, context?: Record<string, unknown>): void {
        this.log('warn', message, context);
    }

    public error(message: string, error?: Error, context?: Record<string, unknown>): void {
        this.log('error', message, context, error);
    }

    // Utility methods
    public setLogLevel(level: LogLevel): void {
        this.logLevel = level;
        this.info(`Log level changed to: ${level}`);
    }

    public getLogHistory(): LogEntry[] {
        return [...this.logHistory];
    }

    public clearHistory(): void {
        this.logHistory = [];
        this.info('Log history cleared');
    }

    public exportLogs(): string {
        return this.logHistory
            .map((entry) => this.formatMessage(entry.level, entry.message, entry.context, entry.error))
            .join('\n');
    }

    // Performance logging
    public startTimer(label: string): () => void {
        const start = performance.now();
        this.debug(`⏱️  Started timer: ${label}`);

        return () => {
            const duration = performance.now() - start;
            this.debug(`⏱️  Timer ${label}: ${duration.toFixed(2)}ms`);
        };
    }

    // API request logging
    public logApiRequest(method: string, url: string, data?: unknown): void {
        this.debug(`🌐 API Request: ${method} ${url}`, { data });
    }

    public logApiResponse(method: string, url: string, status: number, duration: number): void {
        const level = status >= 400 ? 'error' : status >= 300 ? 'warn' : 'debug';
        this.log(level, `🌐 API Response: ${method} ${url} - ${status} (${duration}ms)`, { status, duration });
    }

    // User action logging
    public logUserAction(action: string, details?: Record<string, unknown>): void {
        this.info(`👤 User Action: ${action}`, details);
    }

    // System event logging
    public logSystemEvent(event: string, details?: Record<string, unknown>): void {
        this.info(`⚙️  System Event: ${event}`, details);
    }
}
