// Logger types
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogEntry {
    level: LogLevel;
    message: string;
    timestamp: Date;
    context?: Record<string, unknown>;
    error?: Error;
}

// Main Logger interface
export interface ILogger {
    // Public logging methods
    debug(message: string, context?: Record<string, unknown>): void;
    info(message: string, context?: Record<string, unknown>): void;
    warn(message: string, context?: Record<string, unknown>): void;
    error(message: string, error?: Error, context?: Record<string, unknown>): void;

    // Utility methods
    setLogLevel(level: LogLevel): void;
    getLogHistory(): LogEntry[];
    clearHistory(): void;
    exportLogs(): string;

    // Performance logging
    startTimer(label: string): () => void;

    // API request logging
    logApiRequest(method: string, url: string, data?: unknown): void;
    logApiResponse(method: string, url: string, status: number, duration: number): void;

    // User action logging
    logUserAction(action: string, details?: Record<string, unknown>): void;

    // System event logging
    logSystemEvent(event: string, details?: Record<string, unknown>): void;
}
