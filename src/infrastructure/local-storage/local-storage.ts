import { logger } from '../logging';

class LocalStorageFactory {
    set<T>(key: string, data: T) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
        } catch (error) {
            logger.error('LocalStorageFactory.set: Error saving to localStorage', error as Error);
        }
    }

    get(key: string) {
        const data = localStorage.getItem(key);
        if (!data) return null;

        try {
            return JSON.parse(data); // object or arrays or any parsable object
        } catch {
            return data; // string or any other type
        }
    }

    remove(key: string) {
        localStorage.removeItem(key);
    }
}

export const LocalStorage = new LocalStorageFactory();
