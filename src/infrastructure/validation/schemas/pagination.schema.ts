import { z } from 'zod';

// Pagination schema
export const PaginationSchema = z.object({
    pageSize: z.number(),
    currentPage: z.number(),
    totalPages: z.number(),
    totalCount: z.number(),
    hasPrevious: z.boolean(),
    hasNext: z.boolean(),
});

export type Pagination = z.infer<typeof PaginationSchema>;

export const DEFAULT_PAGINATION: Pagination = {
    pageSize: 0,
    currentPage: 0,
    totalPages: 0,
    totalCount: 0,
    hasPrevious: false,
    hasNext: false,
};
