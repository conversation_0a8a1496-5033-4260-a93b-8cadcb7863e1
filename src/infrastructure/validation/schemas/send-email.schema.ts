import { z } from 'zod';

//-- Email validation regex: allows letters, numbers, and special chars (.$_!~) before @, domain name, and 2-4 char extension
const EMAIL_REGEX = /^[a-zA-z0-9.$_!~]+@[a-zA-Z]+\.[a-z]{2,4}$/g;

//-- Schema
export const sendEmailSchema = z.object({
    email: z.email({ pattern: EMAIL_REGEX, error: 'Please enter a valid email address' }),
    subject: z.string().min(1, 'Subject is required'),
    description: z.string().min(1, 'Description is required'),
});

//-- Export schema types for TypeScript inference
export type SendEmail = z.infer<typeof sendEmailSchema>;
