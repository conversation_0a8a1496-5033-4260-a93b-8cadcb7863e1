import { z } from 'zod';

//-- Email validation regex: allows letters, numbers, and special chars (.$_!~) before @, domain name, and 2-4 char extension
const EMAIL_REGEX = /^[a-zA-z0-9.$_!~]+@[a-zA-Z]+\.[a-z]{2,4}$/g;

//--This regex refer to that, Password must be at least 8 characters and include: uppercase letter, lowercase letter, number, and special character
const PASSWORD_REGEX =
    /^(?=.*[!@#$%^&*(),.?"":{}|<>])(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d!@#$%^&*(),.?"":{}|<>]{8,}$/;

//-- Schema
export const loginCredentialsSchema = z.object({
    username: z.email({ pattern: EMAIL_REGEX, error: 'Please enter a valid email address' }),
    password: z
        .string()
        .regex(
            PASSWORD_REGEX,
            'Password must be at least 8 characters and include: uppercase letter, lowercase letter, number, and special character',
        ),
});

export type LoginCredentials = z.infer<typeof loginCredentialsSchema>;
