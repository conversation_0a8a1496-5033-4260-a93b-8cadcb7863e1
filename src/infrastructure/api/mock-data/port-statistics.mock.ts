import type { PortStatisticsResponse } from '../ports/statistics/types';

// Mock data for different port types to simulate realistic scenarios
export const mockPortStatisticsData: Record<number, PortStatisticsResponse> = {
    // King Fahd International Airport (Air Port)
    1: {
        activeTrips: {
            inbound: 15,
            outbound: 12,
        },
        locksCount: 180,
        trackersCount: 220,
    },

    // Jeddah Islamic Seaport (Sea Port)
    2: {
        activeTrips: {
            inbound: 45,
            outbound: 38,
        },
        locksCount: 520,
        trackersCount: 680,
    },

    // Batha Customs Border (Land Port)
    3: {
        activeTrips: {
            inbound: 28,
            outbound: 31,
        },
        locksCount: 350,
        trackersCount: 420,
    },

    // King <PERSON>port (Sea Port)
    4: {
        activeTrips: {
            inbound: 52,
            outbound: 47,
        },
        locksCount: 650,
        trackersCount: 780,
    },

    // Salwa Customs Border (Land Port)
    5: {
        activeTrips: {
            inbound: 22,
            outbound: 19,
        },
        locksCount: 280,
        trackersCount: 340,
    },
};

// Default mock data for unknown ports
export const defaultMockPortStatistics: PortStatisticsResponse = {
    activeTrips: {
        inbound: Math.floor(Math.random() * 30) + 10,
        outbound: Math.floor(Math.random() * 25) + 8,
    },
    locksCount: Math.floor(Math.random() * 400) + 100,
    trackersCount: Math.floor(Math.random() * 300) + 150,
};
