import type { GetAlertTypesResponse } from '../alert-types/types';

export const dummyAlertTypesResponse: GetAlertTypesResponse = {
    data: [
        {
            id: 2,
            name: {
                arabic: 'العبث بجهاز التتبع',
                english: 'Tracker Tamper',
            },
            alertCode: 1,
            alertPriority: 1,
            origin: 2,
        },
        {
            id: 3,
            name: {
                arabic: 'سقوط جھاز التتبع',
                english: 'Tracker Dropped',
            },
            alertCode: 2,
            alertPriority: 1,
            origin: 2,
        },
        {
            id: 4,
            name: {
                arabic: 'العبث في القفل',
                english: 'Lock Tamper',
            },
            alertCode: 3,
            alertPriority: 1,
            origin: 2,
        },
        {
            id: 5,
            name: {
                arabic: 'تم فتح القفل',
                english: 'Lock Open',
            },
            alertCode: 4,
            alertPriority: 1,
            origin: 2,
        },
        {
            id: 6,
            name: {
                arabic: 'فقدان الإتصال مع القفل',
                english: 'Lock Connection Lost',
            },
            alertCode: 5,
            alertPriority: 1,
            origin: 2,
        },
        {
            id: 7,
            name: {
                arabic: 'بطارية جھاز التتبع ضعيفة',
                english: 'Tracker Battery Low',
            },
            alertCode: 31,
            alertPriority: 2,
            origin: 2,
        },
        {
            id: 8,
            name: {
                arabic: 'بطارية القفل ضعيفة',
                english: 'Lock Low Battery',
            },
            alertCode: 32,
            alertPriority: 2,
            origin: 2,
        },
        {
            id: 9,
            name: {
                arabic: 'بطارية القفل ضعيفة',
                english: 'Lock Very Low Battery',
            },
            alertCode: 33,
            alertPriority: 2,
            origin: 2,
        },
        {
            id: 10,
            name: {
                arabic: 'فقدان إشارة شبكة الإتصال',
                english: 'GSM Signal Lost',
            },
            alertCode: 34,
            alertPriority: 1,
            origin: 2,
        },
        {
            id: 1,
            name: {
                arabic: 'فقدان إشارة تحديد الموقع',
                english: 'GPS Signal Lost',
            },
            alertCode: 35,
            alertPriority: 1,
            origin: 2,
        },
    ],
    pagination: {
        pageSize: 25,
        currentPage: 1,
        totalPages: 1,
        totalCount: 25,
        hasPrevious: false,
        hasNext: false,
    },
};
