import { z } from 'zod';

import { PaginationSchema } from '@/infrastructure/validation/schemas/pagination.schema';
import { TranslatableSchema } from '@/infrastructure/validation/schemas/translatable.schema';
import { MapPointEntryType, MapPointType } from '@/shared/enums';

// ========== QUERY PARAMS SCHEMA ==========
export const GetMapPointsQueryParamsSchema = z.object({
    q: z.string().optional(),
    types: z
        .union([z.string(), z.array(z.string())])
        .optional()
        .transform((val) => {
            if (!val) return undefined;
            const arr = Array.isArray(val) ? val : val.split(',');
            return arr.map((s) => parseInt(s.trim(), 10)).filter((n) => !isNaN(n));
        }),
    entryTypes: z
        .union([z.string(), z.array(z.string())])
        .optional()
        .transform((val) => {
            if (!val) return undefined;
            const arr = Array.isArray(val) ? val : val.split(',');
            return arr.map((s) => parseInt(s.trim(), 10)).filter((n) => !isNaN(n));
        }),
    lat: z.coerce.number().optional(),
    long: z.coerce.number().optional(),
    pageSize: z.coerce.number().min(1).max(100).optional().default(20),
    pageNumber: z.coerce.number().min(1).optional().default(1),
});

// ========== QUERY PARSER ==========
export const ParseMapPointsQueryParams = (raw: URLSearchParams | Record<string, unknown>) => {
    const params =
        raw instanceof URLSearchParams
            ? Object.fromEntries([...raw.entries()].map(([k, v]) => [k, raw.getAll(k).length > 1 ? raw.getAll(k) : v]))
            : raw;

    return GetMapPointsQueryParamsSchema.parse(params);
};

// ========== RESPONSE SCHEMA ==========

export const MapPointDataItemSchema = z.object({
    id: z.coerce.number(),
    name: TranslatableSchema,
    type: z.enum(MapPointType),
    entryType: z.enum(MapPointEntryType),
    lat: z.coerce.number(),
    long: z.coerce.number(),
    contact: z.object({
        name: TranslatableSchema,
        phone: z.string(),
    }),
});

export const GetMapPointsResponseSchema = z.object({
    data: z.array(MapPointDataItemSchema),
    pagination: PaginationSchema,
});
