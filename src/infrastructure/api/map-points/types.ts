import type { z } from 'zod';

import type {
    MapPointDataItemSchema,
    GetMapPointsQueryParamsSchema,
    GetMapPointsResponseSchema,
} from '../map-points/schemas';

/** Type inferred from schema */
export type MapPointDataItem = z.infer<typeof MapPointDataItemSchema>;
export type GetMapPointsResponse = z.infer<typeof GetMapPointsResponseSchema>;
export type GetMapPointsQueryParams = z.infer<typeof GetMapPointsQueryParamsSchema>;
