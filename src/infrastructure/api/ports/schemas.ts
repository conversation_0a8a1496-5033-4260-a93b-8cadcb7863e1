import { z } from 'zod';

import { PaginationSchema } from '@/infrastructure/validation/schemas/pagination.schema';
import { TranslatableSchema } from '@/infrastructure/validation/schemas/translatable.schema';
import { PortType } from '@/shared/enums';

// Simple query params schema
export const GetPortsQueryParamsSchema = z.object({
    q: z.string().optional(),

    types: z
        .union([z.string(), z.array(z.string())])
        .optional()
        .transform((val) => {
            if (!val) return undefined;
            const arr = Array.isArray(val) ? val : val.split(',');
            return arr.map((s) => parseInt(s.trim(), 10)).filter((n) => !isNaN(n));
        }), // TODO: rename to entryTypes to match with Map Points

    lat: z.coerce.number().optional(),
    long: z.coerce.number().optional(),
    code: z.string().optional(),
    sequence: z.coerce.number().optional(),
    isAssignToMe: z.coerce.boolean().optional(),
    pageSize: z.coerce.number().optional(),
    pageNumber: z.coerce.number().optional(),
});

// Simple parser
export function parseQueryParams(raw: URLSearchParams | Record<string, string | string[]>) {
    const normalized: Record<string, unknown> = {};

    if (raw instanceof URLSearchParams) {
        for (const key of new Set(raw.keys())) {
            const vals = raw.getAll(key);
            normalized[key] = vals.length > 1 ? vals : vals[0];
        }
    } else {
        Object.assign(normalized, raw);
    }

    return GetPortsQueryParamsSchema.parse(normalized);
}

// Contact schema
const ContactSchema = z.object({
    id: z.number(),
    type: z.object({
        id: z.number(),
        name: z.string(),
    }),
    name: z.string(),
    phone: z.string().nullable().optional(),
    email: z.string().nullable().optional(),
});

// Main data item schema
export const DataItemSchema = z.object({
    id: z.number(),
    name: TranslatableSchema,
    type: z.enum(PortType),
    lat: z.number(),
    long: z.number(),
    sequence: z.number().nullable(),
    code: z.string(),
    contacts: z.array(ContactSchema),
    assignedUserIds: z.array(z.string()),
});

// Final response schema
export const GetPortsApiResponseSchema = z.object({
    data: z.array(DataItemSchema),
    pagination: PaginationSchema,
});
