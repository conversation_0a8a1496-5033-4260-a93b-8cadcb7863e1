import { z } from 'zod';

// Port Statistics Request Schema
export const PortStatisticsRequestSchema = z.object({
    portId: z.number().positive('Port ID must be a positive number'),
});

// Active Trips Schema
export const ActiveTripsSchema = z.object({
    inbound: z.number().min(0, 'Inbound trips must be non-negative'),
    outbound: z.number().min(0, 'Outbound trips must be non-negative'),
});

// Port Statistics Response Schema
export const PortStatisticsResponseSchema = z.object({
    activeTrips: ActiveTripsSchema,
    locksCount: z.number().min(0, 'Locks count must be non-negative'),
    trackersCount: z.number().min(0, 'Trackers count must be non-negative'),
});

// Error Response Schema
export const PortStatisticsErrorResponseSchema = z.object({
    code: z.string(),
    message: z.string(),
    errors: z.array(z.string()),
});
