import type { z } from 'zod';

import type {
    PortStatisticsRequestSchema,
    PortStatisticsResponseSchema,
    PortStatisticsErrorResponseSchema,
    ActiveTripsSchema,
} from './schemas';

export type PortStatisticsRequest = z.infer<typeof PortStatisticsRequestSchema>;
export type PortStatisticsResponse = z.infer<typeof PortStatisticsResponseSchema>;
export type PortStatisticsErrorResponse = z.infer<typeof PortStatisticsErrorResponseSchema>;
export type ActiveTrips = z.infer<typeof ActiveTripsSchema>;
