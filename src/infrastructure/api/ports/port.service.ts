import { logger } from '@/infrastructure/logging';
import { appConfig } from '@/shared/config/app-settings.config';
import { valy } from '@/shared/lib/Valy';
import { fetchy } from '@/shared/lib/Fetchy';
import { DEFAULT_PAGINATION } from '@/infrastructure/validation/schemas/pagination.schema';

import { PortsData } from '../mock-data/ports.mock';

import type { GetPortsQueryParams, GetPortsApiResponse } from './types';
import { GetPortsApiResponseSchema } from './schemas';

export class PortService {
    private static instance: PortService;

    private constructor() {}

    public static getInstance(): PortService {
        if (!PortService.instance) {
            PortService.instance = new PortService();
        }
        return PortService.instance;
    }

    public async getPorts(params: GetPortsQueryParams): Promise<GetPortsApiResponse> {
        logger.info('[PortService] fetching ports with query params: ', params);

        try {
            if (appConfig.get('mockApiData')) {
                return this.fakeFetchPorts(params);
            }
            return this.realFetchPorts(params);
        } catch (error: unknown) {
            logger.error('[PortService] Error fetching ports: ', error as Error);
            throw error;
        }
    }

    private fakeFetchPorts(params: Partial<GetPortsQueryParams> = {}): Promise<GetPortsApiResponse> {
        logger.info('[PortService] fetching ports with query params: ', params ?? {});
        return Promise.resolve(PortsData);
    }

    private async realFetchPorts(params: Partial<GetPortsQueryParams> = {}): Promise<GetPortsApiResponse> {
        const response = await fetchy.get<GetPortsApiResponse>('ports', { params });
        // todo: cache using react-query

        const validationResult = valy.validate(GetPortsApiResponseSchema, response.data, 'ports');
        if (validationResult.success === false) {
            logger.error(
                '[PortService] Invalid response from ports API',
                new Error('validation error', { cause: validationResult.errors }),
            );
            return {
                data: [],
                pagination: DEFAULT_PAGINATION,
            };
        }

        return validationResult.data as GetPortsApiResponse;
    }
}

export const portService = PortService.getInstance();
