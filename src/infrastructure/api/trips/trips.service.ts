import { logger } from '@/infrastructure/logging';
import { valy } from '@/shared/lib/Valy';
import { fetchy } from '@/shared/lib/Fetchy';
import { appConfig } from '@/shared/config/app-settings.config';

import { TripsData } from '../mock-data/trips';
import { TripDetailMock } from '../mock-data/trip-detail.mock';
import { TripLocationData } from '../mock-data/location.mock';

import { TripsApiResponseSchema } from './schemas';
import type { TripsRequest, TripsResponse } from './types';
import { TripLocationResponseSchema } from './schemas';
import type { TripLocationRequest, TripLocationResponse } from './types';
import { TripDetailSchema } from './schemas';
import type { GetTripDetailQueryParams, TripDetail } from './types';

export class TripsService {
    private static instance: TripsService;

    private constructor() {}

    public static getInstance(): TripsService {
        if (!TripsService.instance) {
            TripsService.instance = new TripsService();
        }
        return TripsService.instance;
    }

    public async getTrips(params: TripsRequest): Promise<TripsResponse> {
        logger.info('[TripsService] fetching trips with params: ', params);

        try {
            if (appConfig.get('mockApiData')) {
                return this.fakeFetchTrips(params);
            }
            return this.realFetchTrips(params);
        } catch (error: unknown) {
            logger.error('[TripsService] Error fetching trips: ', error as Error);
            throw error;
        }
    }

    private fakeFetchTrips(params: TripsRequest): Promise<TripsResponse> {
        logger.info('[TripsService] fetching trips with params: ', params);
        return Promise.resolve(TripsData);
    }

    private async realFetchTrips(params: TripsRequest): Promise<TripsResponse> {
        const response = await fetchy.post<TripsResponse>('/trips', { body: params });
        // todo cache using react-query

        const validationResult = valy.validate(TripsApiResponseSchema, response.data, 'trips_response');
        if (validationResult.success === false) {
            logger.error(
                '[TripsService] Invalid response from trips API',
                new Error('validation error', { cause: validationResult.errors }),
            );
            return TripsData; // Return mock data as fallback
        }

        return validationResult.data as TripsResponse;
    }

    public async getTripLocations(params: TripLocationRequest): Promise<TripLocationResponse | null> {
        logger.info('[TripsService] fetching trip locations with params: ', params);

        try {
            if (appConfig.get('mockApiData')) {
                return this.fakeFetchTripLocations(params);
            }
            return this.realFetchTripLocations(params);
        } catch (error: unknown) {
            logger.error('[TripsService] Error fetching trip locations: ', error as Error);
            throw error;
        }
    }

    private fakeFetchTripLocations(params: TripLocationRequest): Promise<TripLocationResponse> {
        logger.info('[TripsService] fetching trip locations with params: ', params);
        return Promise.resolve(TripLocationData);
    }

    private async realFetchTripLocations(params: TripLocationRequest): Promise<TripLocationResponse | null> {
        const response = await fetchy.post<TripLocationResponse>('/trips/location', { body: params });
        // todo cache using react-query

        if (response.status !== 200) {
            logger.error(
                '[TripsService] Invalid response from trip locations API',
                new Error(`api return with error status ${response.status}`),
            );
            return null;
        }

        const validationResult = valy.validate(TripLocationResponseSchema, response.data, 'trip_location_response');
        if (validationResult.success === false) {
            logger.error(
                '[TripsService] Invalid response from trip locations API',
                new Error('validation error', { cause: validationResult.errors }),
            );
            return TripLocationData; // Return mock data as fallback
        }

        return validationResult.data as TripLocationResponse;
    }

    public async getTripDetail(params: GetTripDetailQueryParams): Promise<TripDetail | null> {
        logger.info('[TripsService] fetching trip detail with ID: ', params);

        try {
            if (appConfig.get('mockApiData')) {
                return this.fakeFetchTripDetail(params);
            }
            return this.realFetchTripDetail(params);
        } catch (error: unknown) {
            logger.error('[TripsService] Error fetching trip detail: ', error as Error);
            throw error;
        }
    }

    private async fakeFetchTripDetail(params: Partial<GetTripDetailQueryParams> = {}): Promise<TripDetail> {
        logger.info('[TripsService] returning mocked trip detail for ID:', params ?? {});
        return Promise.resolve(TripDetailMock);
    }

    private async realFetchTripDetail(params: Partial<GetTripDetailQueryParams> = {}): Promise<TripDetail | null> {
        const response = await fetchy.get<TripDetail>(`trips/${params.id}`);

        const validationResult = valy.validate(TripDetailSchema, response.data, 'trip_detail');
        if (validationResult.success === false) {
            logger.error(
                '[TripsService] Invalid response from trip detail API',
                new Error('validation error', { cause: validationResult.errors }),
            );
            return null;
        }

        return validationResult.data as TripDetail;
    }
}

export const tripsService = TripsService.getInstance();
