import type { z } from 'zod';

import type {
    TripLocationRequestSchema,
    TripLocationResponseSchema,
    TripLocationSchema,
    AlertSchema,
    GetTripDetailQueryParamsSchema,
    PortSchema,
    StateChangeSchema,
    TripDetailSchema,
    TripStateSchema,
} from './schemas';
import type { TripsApiResponseSchema, TripsItemSchema } from './schemas';

export type TripsItem = z.infer<typeof TripsItemSchema>;
export type TripsResponse = z.infer<typeof TripsApiResponseSchema>;
export type TripsRequest = z.infer<typeof TripLocationRequestSchema>;

export type TripLocationRequest = z.infer<typeof TripLocationRequestSchema>;
export type TripLocationResponse = z.infer<typeof TripLocationResponseSchema>;

export type TripLocationItem = z.infer<typeof TripLocationSchema>;

export type TripDetail = z.infer<typeof TripDetailSchema>;
export type GetTripDetailQueryParams = z.infer<typeof GetTripDetailQueryParamsSchema>;
export type Port = z.infer<typeof PortSchema>;
export type Alert = z.infer<typeof AlertSchema>;
export type StateChange = z.infer<typeof StateChangeSchema>;
export type TripState = z.infer<typeof TripStateSchema>;
