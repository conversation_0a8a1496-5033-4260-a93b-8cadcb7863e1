import { z } from 'zod';

import { TranslatableSchema } from '@/infrastructure/validation/schemas/translatable.schema';
import { PaginationSchema } from '@/infrastructure/validation/schemas/pagination.schema';
import { AlertOrigin } from '@/shared/enums';

export const AlertTypeQueryParamsSchema = z.object({
    Q: z.string().nullable().default(null),
    PageSize: z.number().default(100),
    PageNumber: z.number().default(1),
});

export const AlertTypeSchema = z.object({
    id: z.coerce.number(),
    name: TranslatableSchema,
    alertCode: z.coerce.number().nullable().optional(),
    alertPriority: z.coerce.number(),
    origin: z.enum(AlertOrigin),
});

export const GetAlertTypesResponseSchema = z.object({
    data: z.array(AlertTypeSchema),
    pagination: PaginationSchema,
});
