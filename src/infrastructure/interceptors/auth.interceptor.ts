import { fetchy } from '@/shared/lib/Fetchy';

import { LocalStorage } from '../local-storage/local-storage';
import { LocalStorageKeys } from '../local-storage/local-storage-keys.constants';

fetchy.addRequestInterceptor((config) => {
    const authToken: string | null = LocalStorage.get(LocalStorageKeys.AUTH_TOKEN);

    if (authToken == null || authToken?.length == 0) return config;
    config.headers.Authorization = `Bearer ${authToken}`;

    return config;
});
