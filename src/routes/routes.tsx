import React from 'react';
import { Route, Routes } from 'react-router-dom';

import Layout from '@/components/features/layout/Layout';

/**
 * AppRoutes Component
 * Defines the main routing structure for the application
 * Handles navigation between different pages/components
 */

//-- Lazy Loading
const LazyLogin = React.lazy(() => import('../components/pages/login/Login'));
const LazyTrips = React.lazy(() => import('../components/pages/Trips'));
const LazyAlerts = React.lazy(() => import('../components/pages/Alerts'));
const LazyDashboard = React.lazy(() => import('../components/pages/Dashboard'));
const LazyMonitorBoard = React.lazy(() => import('../components/pages/MonitorBoard'));
const LazyTripInformation = React.lazy(() => import('../components/pages/TripInformation'));
const LazyPageNotFound = React.lazy(() => import('../components/pages/PageNotFound'));

export default function AppRoutes() {
    return (
        <Routes>
            {/* renders into the outlet in <Layout> at "/" */}
            <Route element={<Layout />}>
                <Route path="/Trips" element={<LazyTrips />} />
                <Route path="/dashboard" element={<LazyDashboard />} />
                <Route path="/reports/alerts" element={<LazyAlerts />} />
                <Route path="/monitor-board" element={<LazyMonitorBoard />} />
                <Route path="/trip-information" element={<LazyTripInformation />} />
            </Route>

            <Route path="login" element={<LazyLogin />} />
            <Route path="*" element={<LazyPageNotFound />} />
        </Routes>
    );
}
