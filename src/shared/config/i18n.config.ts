import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';

import { LocalStorageKeys } from '@/infrastructure/local-storage/local-storage-keys.constants';

i18n.use(Backend)
    .use(LanguageDetector)
    .use(initReactI18next)
    .init({
        debug: false,
        fallbackLng: 'en',
        defaultNS: 'translation', // default namespace
        detection: {
            order: ['localStorage', 'navigator'],
            caches: ['localStorage'],
            lookupLocalStorage: LocalStorageKeys.LANG, // local storage key
        },
    });

export default i18n;
