/* eslint-disable @typescript-eslint/no-unused-vars */
declare global {
    interface Window {
        google: typeof google;
    }
}

declare namespace google {
    namespace maps {
        class Map {
            constructor(mapDiv: HTMLElement, opts?: MapOptions);
        }

        class Marker {
            constructor(opts?: MarkerOptions);
        }

        interface MapOptions {
            center?: LatLng | LatLngLiteral;
            zoom?: number;
            mapTypeId?: MapTypeId;
            mapTypeControl?: boolean;
            streetViewControl?: boolean;
            fullscreenControl?: boolean;
            zoomControl?: boolean;
            gestureHandling?: string;
            styles?: MapTypeStyle[];
        }

        interface MarkerOptions {
            position?: LatLng | LatLngLiteral;
            map?: Map;
            title?: string;
        }

        interface LatLng {
            lat(): number;
            lng(): number;
        }

        interface LatLngLiteral {
            lat: number;
            lng: number;
        }

        enum MapTypeId {
            ROADMAP = 'roadmap',
            SATELLITE = 'satellite',
            HYBRID = 'hybrid',
            TERRAIN = 'terrain',
        }

        interface MapTypeStyle {
            featureType?: string;
            elementType?: string;
            stylers?: Array<{ [key: string]: unknown }>;
        }
    }
}

export {};
