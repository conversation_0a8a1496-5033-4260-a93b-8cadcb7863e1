import toast from 'react-hot-toast';
import type { z } from 'zod';
import type { $ZodIssue } from 'zod/v4/core';

import type { ILogger } from '@/infrastructure/logging';

import type { ValidationError, ValidationResult } from './types';

const fireFeedback = (message: string) => {
    toast(message, { duration: 5_000, position: 'top-right' });
};

export class Validator {
    private static instance: Validator;

    private logger: ILogger | null;

    private constructor() {
        this.logger = null;
    }

    public static getInstance(): Validator {
        if (!Validator.instance) {
            Validator.instance = new Validator();
        }
        return Validator.instance;
    }

    public setLogger(logger: ILogger | null): void {
        this.logger = logger;
    }

    //-- Generic validation method that can validate any data against any Zod schema
    public validate<T>(schema: z.ZodSchema<T>, data: unknown, context?: string): ValidationResult<T> {
        const timer = this.logger?.startTimer(`Validation${context ? ` - ${context}` : ''}`);

        try {
            const result = schema.safeParse(data);
            timer?.();

            if (result.success) {
                this.logger?.debug(`Validation successful${context ? ` for ${context}` : ''}`, { data: result.data });
                return {
                    success: true,
                    data: result.data,
                };
            } else {
                let composedErrorMessage = '';
                const errors: ValidationError[] = result.error.issues.map((err: $ZodIssue) => {
                    composedErrorMessage = `${composedErrorMessage && composedErrorMessage + '\n\n'} ❌ ${err.message}`;
                    const feedback = {
                        field: err.path.join('.') || 'root',
                        message: err.message,
                        code: err.code,
                    };

                    return feedback;
                });

                //-- Fire toast message to fire user feedback
                fireFeedback(composedErrorMessage);

                this.logger?.warn(`Validation failed${context ? ` for ${context}` : ''}`, {
                    errors,
                    originalData: data,
                });

                return {
                    success: false,
                    errors,
                };
            }
        } catch (error) {
            timer?.();
            this.logger?.error(`Validation error${context ? ` for ${context}` : ''}`, error as Error, { data });

            return {
                success: false,
                errors: [
                    {
                        field: 'validation',
                        message: 'An unexpected validation error occurred',
                        code: 'VALIDATION_ERROR',
                    },
                ],
            };
        }
    }
}
