import type { ValidationError } from '../types';

export function toError(error: ValidationError): Error {
    return new Error(error.message, { cause: error });
}

export function toErrors(errors: ValidationError[]): Error[] {
    return errors.map((e) => new Error(e.message, { cause: e }));
}

export function toCombinedError(errors: ValidationError[]): Error {
    return new Error(errors.map((e) => e.message).join('\n'), { cause: errors });
}
