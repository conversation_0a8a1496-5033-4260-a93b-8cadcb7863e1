import { Validator } from './valy';
import { toError, toErrors, toCombinedError } from './utils/converters';
import type { ValidationError, ValidationResult } from './types';

const _valy = Validator.getInstance();

export type { ValidationError, ValidationResult };
export const valy = {
    validate: _valy.validate.bind(_valy),
    setLogger: _valy.setLogger.bind(_valy),
    toCombinedError,
    toError,
    toErrors,
};
