# Fetchy - HTTP Client Library

A lightweight HTTP client library with built-in support for file uploads, FormData, and React Query integration.

## Features

- ✅ HTTP methods (GET, POST, PUT, PATCH, DELETE)
- ✅ File upload support with FormData
- ✅ Automatic JSON/FormData detection
- ✅ Authentication token handling
- ✅ Request/response logging
- ✅ Error handling with retries
- ✅ React Query integration
- ✅ TypeScript support

## Basic Usage

```typescript
import { fetchy } from './shared/lib/Fetchy';

// GET request
const response = await fetchy.get<User[]>('/users', { page: 1 });

// POST request with JSON
const newUser = await fetchy.post<User>('/users', {
  name: '<PERSON>',
  email: '<EMAIL>'
});
```

## File Upload Usage

### Method 1: Using the upload() method

```typescript
// Single file upload
const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement;
const file = fileInput.files[0];

const response = await fetchy.upload<UploadResponse>('/upload', {
  files: file,
  fields: {
    description: 'My uploaded file',
    category: 'documents'
  },
  onUploadProgress: (progress) => {
    console.log(`Upload progress: ${progress}%`);
  }
});

// Multiple files upload
const files = Array.from(fileInput.files);
const response = await fetchy.upload<UploadResponse>('/upload-multiple', {
  files: files,
  fields: {
    description: 'Multiple files'
  }
});
```

### Method 2: Using FormData directly

```typescript
const formData = new FormData();
formData.append('file', file);
formData.append('description', 'My file');

const response = await fetchy.uploadFormData<UploadResponse>('/upload', formData);
```

### Method 3: Using post() with FormData

```typescript
const formData = new FormData();
formData.append('file', file);

// The library automatically detects FormData and handles it properly
const response = await fetchy.post<UploadResponse>('/upload', formData);
```

## Configuration

```typescript
import { Fetchy } from './shared/lib/Fetchy';

// Initialize with custom config
const customFetchy = Fetchy.getInstance({
  baseURL: 'https://api.example.com',
  timeout: 60000,
  retries: 5,
  retryDelay: 2000
});
```

## React Query Integration

```typescript
import { useQuery, useMutation } from '@tanstack/react-query';
import { fetchy } from './shared/lib/Fetchy';

// Query
const { data, isLoading } = useQuery({
  queryKey: ['users'],
  queryFn: () => fetchy.get<User[]>('/users').then(res => res.data)
});

// Mutation
const mutation = useMutation({
  mutationFn: (user: CreateUser) => 
    fetchy.post<User>('/users', user).then(res => res.data)
});
```

## Error Handling

```typescript
try {
  const response = await fetchy.upload<UploadResponse>('/upload', {
    files: file
  });
} catch (error) {
  if (error.code === 'TIMEOUT') {
    console.log('Upload timed out');
  } else if (error.code === 'NETWORK_ERROR') {
    console.log('Network error occurred');
  } else {
    console.log('Upload failed:', error.message);
  }
}
```

## Types

```typescript
interface UploadOptions {
  files: File | File[];
  fields?: Record<string, string>;
  onUploadProgress?: (progress: number) => void;
}

interface FetchyResponse<T> {
  data: T;
  status: number;
  statusText: string;
  headers: Record<string, string>;
}
``` 