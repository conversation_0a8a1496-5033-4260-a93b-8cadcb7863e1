import { QueryClient } from '@tanstack/react-query';

import type { ILogger } from '@/infrastructure/logging';

import type { FetchyConfig } from '../types';

export class QueryClientWrapper {
    private client: QueryClient;
    private logger: ILogger | null = null;

    public constructor(config: FetchyConfig, logger: ILogger | null = null) {
        this.logger = logger;

        this.client = new QueryClient({
            defaultOptions: {
                queries: {
                    retry: config.retries,
                    retryDelay: config.retryDelay,
                    staleTime: 5 * 60 * 1000, // 5 minutes
                    gcTime: 10 * 60 * 1000, // 10 minutes
                },
                mutations: {
                    retry: config.retries,
                    retryDelay: config.retryDelay,
                },
            },
        });
    }

    // Query Client methods for advanced usage
    public invalidateQueries(queryKey: string[]): Promise<void> {
        this.logger?.debug('Invalidating queries', { queryKey });
        return this.client.invalidateQueries({ queryKey });
    }

    public removeQueries(queryKey: string[]): void {
        this.logger?.debug('Removing queries', { queryKey });
        this.client.removeQueries({ queryKey });
    }

    public prefetchQuery<T>(
        queryKey: string[],
        queryFn: () => Promise<T>,
        options?: { staleTime?: number },
    ): Promise<void> {
        this.logger?.debug('Prefetching query', { queryKey });
        return this.client.prefetchQuery({
            queryKey,
            queryFn,
            staleTime: options?.staleTime,
        });
    }

    public getQueryClient(): QueryClient {
        return this.client;
    }
}
