/* eslint-disable @typescript-eslint/no-explicit-any */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { QueryClient } from '@tanstack/react-query';
import axios, { type AxiosResponse, type AxiosError } from 'axios';

import { Fetchy } from './fetchy';
import { DEFAULT_CONFIG } from './utils/defaultOptions';

// Mock axios
vi.mock('axios');
const mockedAxios = vi.mocked(axios);

// Mock QueryClientWrapper
vi.mock('./utils/queryClient', () => ({
    QueryClientWrapper: vi.fn().mockImplementation(() => ({
        invalidateQueries: vi.fn(),
        removeQueries: vi.fn(),
        prefetchQuery: vi.fn(),
        getQueryClient: vi.fn(() => new QueryClient()),
    })),
}));

// Mock logger
const mockLogger = {
    info: vi.fn(),
    debug: vi.fn(),
    error: vi.fn(),
    logApiRequest: vi.fn(),
    logApiResponse: vi.fn(),
};

describe('Fetchy', () => {
    let fetchy: Fetchy;
    let mockAxiosInstance: any;
    let mockAxiosCreate: any;

    beforeEach(() => {
        // Reset all mocks
        vi.clearAllMocks();

        // Reset singleton instance
        (Fetchy as any).instance = undefined;

        // Setup mock axios instance
        mockAxiosInstance = {
            request: vi.fn(),
            interceptors: {
                request: {
                    use: vi.fn(),
                },
                response: {
                    use: vi.fn(),
                },
            },
            defaults: {
                headers: {
                    common: {},
                },
            },
        };

        mockAxiosCreate = vi.fn(() => mockAxiosInstance);
        mockedAxios.create = mockAxiosCreate;
    });

    afterEach(() => {
        vi.clearAllMocks();
    });

    describe('Singleton Pattern', () => {
        it('should return the same instance when called multiple times', () => {
            const instance1 = Fetchy.getInstance();
            const instance2 = Fetchy.getInstance();

            expect(instance1).toBe(instance2);
        });

        it('should allow reconfiguration of existing instance', () => {
            const newConfig = { baseURL: 'https://new-api.example.com' };
            const instance = Fetchy.getInstance(newConfig);

            expect(instance.getConfig().baseURL).toBe('https://new-api.example.com');
        });
    });

    describe('Configuration', () => {
        it('should use default config when no config provided', () => {
            const instance = Fetchy.getInstance();
            const config = instance.getConfig();

            expect(config).toEqual(DEFAULT_CONFIG);
        });

        it('should merge provided config with defaults', () => {
            const customConfig = {
                baseURL: 'https://custom-api.example.com',
                timeout: 10000,
            };

            const instance = Fetchy.getInstance(customConfig);
            const config = instance.getConfig();

            expect(config.baseURL).toBe('https://custom-api.example.com');
            expect(config.timeout).toBe(10000);
            expect(config.retries).toBe(DEFAULT_CONFIG.retries);
        });

        it('should update configuration', () => {
            const newConfig = {
                baseURL: 'https://updated-api.example.com',
                timeout: 15000,
            };

            fetchy = Fetchy.getInstance();
            fetchy.updateConfig(newConfig);
            const config = fetchy.getConfig();

            expect(config.baseURL).toBe('https://updated-api.example.com');
            expect(config.timeout).toBe(15000);
        });
    });

    describe('HTTP Methods', () => {
        beforeEach(() => {
            // Setup successful response
            const mockResponse: AxiosResponse = {
                data: { data: 'test' },
                status: 200,
                statusText: 'OK',
                headers: { 'content-type': 'application/json' },
                config: {} as any,
            };

            mockAxiosInstance.request.mockResolvedValue(mockResponse);

            fetchy = Fetchy.getInstance({
                baseURL: 'https://api.example.com',
                timeout: 5000,
                Logger: mockLogger as any,
            });
        });

        it('should make GET request successfully', async () => {
            const response = await fetchy.get<{ data: string }>('/test');

            expect(mockAxiosInstance.request).toHaveBeenCalledWith({
                url: '/test',
                method: 'GET',
                params: undefined,
                headers: undefined,
                data: undefined,
                timeout: 5000,
            });

            expect(response).toEqual({
                data: { data: 'test' },
                status: 200,
                statusText: 'OK',
                headers: { 'content-type': 'application/json' },
            });
        });

        it('should make POST request successfully', async () => {
            const postData = { name: 'test', value: 123 };
            await fetchy.post<{ data: string }>('/test', {
                body: postData,
            });

            expect(mockAxiosInstance.request).toHaveBeenCalledWith({
                url: '/test',
                method: 'POST',
                params: undefined,
                headers: undefined,
                data: postData,
                timeout: 5000,
            });
        });

        it('should make PUT request successfully', async () => {
            const putData = { id: 1, name: 'updated' };
            const mockResponse: AxiosResponse = {
                data: { success: true, updated: putData },
                status: 200,
                statusText: 'OK',
                headers: { 'content-type': 'application/json' },
                config: {} as any,
            };

            mockAxiosInstance.request.mockResolvedValue(mockResponse);

            const response = await fetchy.put<{ success: boolean; updated: typeof putData }>('/test/1', {
                body: putData,
            });

            expect(mockAxiosInstance.request).toHaveBeenCalledWith({
                url: '/test/1',
                method: 'PUT',
                params: undefined,
                headers: undefined,
                data: putData,
                timeout: 5000,
            });

            expect(response).toEqual({
                data: { success: true, updated: putData },
                status: 200,
                statusText: 'OK',
                headers: { 'content-type': 'application/json' },
            });
        });

        it('should make PUT request with custom headers and timeout', async () => {
            const putData = { id: 1, name: 'updated' };
            const customHeaders = { 'X-Custom-Header': 'custom-value' };
            const customTimeout = 10000;

            const mockResponse: AxiosResponse = {
                data: { success: true },
                status: 200,
                statusText: 'OK',
                headers: {},
                config: {} as any,
            };

            mockAxiosInstance.request.mockResolvedValue(mockResponse);

            await fetchy.put<{ success: boolean }>('/test/1', {
                body: putData,
                headers: customHeaders,
                timeout: customTimeout,
            });

            expect(mockAxiosInstance.request).toHaveBeenCalledWith({
                url: '/test/1',
                method: 'PUT',
                params: undefined,
                headers: customHeaders,
                data: putData,
                timeout: customTimeout,
            });
        });

        it('should handle PUT request errors', async () => {
            const putData = { id: 1, name: 'updated' };
            const putError: AxiosError = {
                code: 'ERR_BAD_RESPONSE',
                message: 'Request failed with status code 400',
                name: 'AxiosError',
                isAxiosError: true,
                toJSON: vi.fn(),
                config: {} as any,
                response: {
                    data: { error: 'Bad Request' },
                    status: 400,
                    statusText: 'Bad Request',
                    headers: {},
                    config: {} as any,
                },
            };

            mockAxiosInstance.request.mockRejectedValue(putError);

            const response = await fetchy.put<{ error: string }>('/test/1', {
                body: putData,
            });

            expect(response).toEqual({
                data: {
                    message: { error: 'Bad Request' },
                    code: '400',
                    details: { error: 'Bad Request' },
                },
                status: 400,
                statusText: 'Bad Request',
                headers: {},
            });
        });

        it('should make PATCH request successfully', async () => {
            const patchData = { name: 'patched' };
            const mockResponse: AxiosResponse = {
                data: { success: true, patched: patchData },
                status: 200,
                statusText: 'OK',
                headers: { 'content-type': 'application/json' },
                config: {} as any,
            };

            mockAxiosInstance.request.mockResolvedValue(mockResponse);

            const response = await fetchy.patch<{ success: boolean; patched: typeof patchData }>('/test/1', {
                body: patchData,
            });

            expect(mockAxiosInstance.request).toHaveBeenCalledWith({
                url: '/test/1',
                method: 'PATCH',
                params: undefined,
                headers: undefined,
                data: patchData,
                timeout: 5000,
            });

            expect(response).toEqual({
                data: { success: true, patched: patchData },
                status: 200,
                statusText: 'OK',
                headers: { 'content-type': 'application/json' },
            });
        });

        it('should make DELETE request successfully', async () => {
            const mockResponse: AxiosResponse = {
                data: { success: true, deleted: true },
                status: 200,
                statusText: 'OK',
                headers: { 'content-type': 'application/json' },
                config: {} as any,
            };

            mockAxiosInstance.request.mockResolvedValue(mockResponse);

            const response = await fetchy.delete<{ success: boolean; deleted: boolean }>('/test/1');

            expect(mockAxiosInstance.request).toHaveBeenCalledWith({
                url: '/test/1',
                method: 'DELETE',
                params: undefined,
                headers: undefined,
                data: undefined,
                timeout: 5000,
            });

            expect(response).toEqual({
                data: { success: true, deleted: true },
                status: 200,
                statusText: 'OK',
                headers: { 'content-type': 'application/json' },
            });
        });

        it('should handle query parameters for GET requests', async () => {
            const params = { page: 1, limit: 10, search: 'test' };
            await fetchy.get<{ data: string }>('/test', { params });

            expect(mockAxiosInstance.request).toHaveBeenCalledWith({
                url: '/test',
                method: 'GET',
                params,
                headers: undefined,
                data: undefined,
                timeout: 5000,
            });
        });

        it('should handle custom timeout', async () => {
            await fetchy.get('/test', { timeout: 10000 });

            expect(mockAxiosInstance.request).toHaveBeenCalledWith({
                url: '/test',
                method: 'GET',
                params: undefined,
                headers: undefined,
                data: undefined,
                timeout: 10000,
            });
        });
    });

    describe('Error Handling', () => {
        beforeEach(() => {
            fetchy = Fetchy.getInstance({
                baseURL: 'https://api.example.com',
                timeout: 5000,
                Logger: mockLogger as any,
            });
        });

        it('should handle timeout errors', async () => {
            const timeoutError: AxiosError = {
                code: 'ECONNABORTED',
                message: 'timeout of 5000ms exceeded',
                name: 'AxiosError',
                isAxiosError: true,
                toJSON: vi.fn(),
                config: {} as any,
            };

            mockAxiosInstance.request.mockRejectedValue(timeoutError);

            const response = await fetchy.get('/test');

            expect(response).toEqual({
                data: {
                    message: 'Request timeout',
                    code: 'TIMEOUT',
                    details: { timeout: 5000 },
                },
                status: 408,
                statusText: 'Request Timeout',
                headers: {},
            });
        });

        it('should handle HTTP error responses', async () => {
            const httpError: AxiosError = {
                code: 'ERR_BAD_RESPONSE',
                message: 'Request failed with status code 404',
                name: 'AxiosError',
                isAxiosError: true,
                toJSON: vi.fn(),
                config: {} as any,
                response: {
                    data: { error: 'Resource not found' },
                    status: 404,
                    statusText: 'Not Found',
                    headers: { 'content-type': 'application/json' },
                    config: {} as any,
                },
            };

            mockAxiosInstance.request.mockRejectedValue(httpError);

            const response = await fetchy.get('/test');

            expect(response).toEqual({
                data: {
                    message: { error: 'Resource not found' },
                    code: '404',
                    details: { error: 'Resource not found' },
                },
                status: 404,
                statusText: 'Not Found',
                headers: { 'content-type': 'application/json' },
            });
        });

        it('should handle network errors', async () => {
            const networkError: AxiosError = {
                code: 'ERR_NETWORK',
                message: 'Network Error',
                name: 'AxiosError',
                isAxiosError: true,
                toJSON: vi.fn(),
                config: {} as any,
            };

            mockAxiosInstance.request.mockRejectedValue(networkError);

            const response = await fetchy.get('/test');

            expect(response).toEqual({
                data: {
                    message: 'Network Error',
                    code: 'UNKNOWN',
                    details: {},
                },
                status: 500,
                statusText: 'Internal Server Error',
                headers: {},
            });
        });
    });

    describe('Response Handling', () => {
        beforeEach(() => {
            fetchy = Fetchy.getInstance({
                baseURL: 'https://api.example.com',
                timeout: 5000,
                Logger: mockLogger as any,
            });
        });

        it('should parse JSON responses', async () => {
            const jsonData = { id: 1, name: 'test' };
            const mockResponse: AxiosResponse = {
                data: jsonData,
                status: 200,
                statusText: 'OK',
                headers: { 'content-type': 'application/json' },
                config: {} as any,
            };

            mockAxiosInstance.request.mockResolvedValue(mockResponse);

            const response = await fetchy.get<typeof jsonData>('/test');

            expect(response.data).toEqual(jsonData);
        });

        it('should include response headers', async () => {
            const headers = {
                'content-type': 'application/json',
                'x-custom-header': 'custom-value',
            };

            const mockResponse: AxiosResponse = {
                data: {},
                status: 200,
                statusText: 'OK',
                headers,
                config: {} as any,
            };

            mockAxiosInstance.request.mockResolvedValue(mockResponse);

            const response = await fetchy.get('/test');

            expect(response.headers).toEqual(headers);
        });
    });

    describe('Interceptors', () => {
        beforeEach(() => {
            fetchy = Fetchy.getInstance({
                baseURL: 'https://api.example.com',
                timeout: 5000,
                Logger: mockLogger as any,
            });
        });

        it('should add request interceptors and return interceptor ID', () => {
            const mockInterceptor = vi.fn();
            const mockErrorInterceptor = vi.fn();
            const mockInterceptorId = 123;

            mockAxiosInstance.interceptors.request.use.mockReturnValue(mockInterceptorId);

            const result = fetchy.addRequestInterceptor(mockInterceptor, mockErrorInterceptor);

            expect(mockAxiosInstance.interceptors.request.use).toHaveBeenCalledWith(
                mockInterceptor,
                mockErrorInterceptor,
            );
            expect(result).toBe(mockInterceptorId);
        });

        it('should add request interceptor with only success handler', () => {
            const mockInterceptor = vi.fn();
            const mockInterceptorId = 456;

            mockAxiosInstance.interceptors.request.use.mockReturnValue(mockInterceptorId);

            const result = fetchy.addRequestInterceptor(mockInterceptor);

            expect(mockAxiosInstance.interceptors.request.use).toHaveBeenCalledWith(mockInterceptor, undefined);
            expect(result).toBe(mockInterceptorId);
        });

        it('should add request interceptor with only error handler', () => {
            const mockErrorInterceptor = vi.fn();
            const mockInterceptorId = 789;

            mockAxiosInstance.interceptors.request.use.mockReturnValue(mockInterceptorId);

            const result = fetchy.addRequestInterceptor(undefined, mockErrorInterceptor);

            expect(mockAxiosInstance.interceptors.request.use).toHaveBeenCalledWith(undefined, mockErrorInterceptor);
            expect(result).toBe(mockInterceptorId);
        });
    });

    describe('Query Client Integration', () => {
        beforeEach(() => {
            fetchy = Fetchy.getInstance({
                baseURL: 'https://api.example.com',
                timeout: 5000,
                Logger: mockLogger as any,
            });
        });

        it('should return QueryClient instance', () => {
            const queryClient = fetchy.queryClient.getQueryClient();

            expect(queryClient).toBeInstanceOf(QueryClient);
        });

        it('should invalidate queries', async () => {
            await fetchy.queryClient.invalidateQueries(['test']);

            expect(fetchy.queryClient.invalidateQueries).toHaveBeenCalledWith(['test']);
        });

        it('should remove queries', () => {
            fetchy.queryClient.removeQueries(['test']);

            expect(fetchy.queryClient.removeQueries).toHaveBeenCalledWith(['test']);
        });

        it('should prefetch queries', async () => {
            const queryFn = vi.fn().mockResolvedValue({ data: 'test' });

            await fetchy.queryClient.prefetchQuery(['test'], queryFn, { staleTime: 60000 });

            expect(fetchy.queryClient.prefetchQuery).toHaveBeenCalledWith(['test'], queryFn, { staleTime: 60000 });
        });
    });

    describe('Logging', () => {
        beforeEach(() => {
            fetchy = Fetchy.getInstance({
                baseURL: 'https://api.example.com',
                timeout: 5000,
                Logger: mockLogger as any,
            });
        });

        it('should log API requests', async () => {
            const mockResponse: AxiosResponse = {
                data: {},
                status: 200,
                statusText: 'OK',
                headers: {},
                config: {} as any,
            };

            mockAxiosInstance.request.mockResolvedValue(mockResponse);

            await fetchy.post('/test', { body: { test: 'data' } });

            expect(mockLogger.logApiRequest).toHaveBeenCalledWith('POST', '/test', {
                test: 'data',
            });
        });

        it('should log initialization', () => {
            Fetchy.getInstance({
                baseURL: 'https://api.example.com',
                Logger: mockLogger as any,
            });

            expect(mockLogger.info).toHaveBeenCalledWith('Fetchy initialized', {
                config: expect.objectContaining({
                    baseURL: 'https://api.example.com',
                }),
            });
        });
    });

    describe('Header Management', () => {
        beforeEach(() => {
            fetchy = Fetchy.getInstance({
                baseURL: 'https://api.example.com',
                timeout: 5000,
                Logger: mockLogger as any,
            });
        });

        it('should set locale header', () => {
            fetchy.setLocaleHeader('en-US');

            expect(mockAxiosInstance.defaults.headers.common['Accept-Language']).toBe('en-US');
        });

        it('should set auth header', () => {
            fetchy.setAuthHeader('Bearer token123');

            expect(mockAxiosInstance.defaults.headers.common['Authorization']).toBe('Bearer token123');
        });
    });

    describe('Edge Cases', () => {
        beforeEach(() => {
            fetchy = Fetchy.getInstance({
                baseURL: 'https://api.example.com',
                timeout: 5000,
                Logger: mockLogger as any,
            });
        });

        it('should handle empty query parameters', async () => {
            const mockResponse: AxiosResponse = {
                data: {},
                status: 200,
                statusText: 'OK',
                headers: {},
                config: {} as any,
            };

            mockAxiosInstance.request.mockResolvedValue(mockResponse);

            await fetchy.get('/test', { params: {} });

            expect(mockAxiosInstance.request).toHaveBeenCalledWith({
                url: '/test',
                method: 'GET',
                params: {},
                headers: undefined,
                data: undefined,
                timeout: 5000,
            });
        });

        it('should handle null/undefined body', async () => {
            const mockResponse: AxiosResponse = {
                data: {},
                status: 200,
                statusText: 'OK',
                headers: {},
                config: {} as any,
            };

            mockAxiosInstance.request.mockResolvedValue(mockResponse);

            await fetchy.post('/test', { body: null });

            expect(mockAxiosInstance.request).toHaveBeenCalledWith({
                url: '/test',
                method: 'POST',
                params: undefined,
                headers: undefined,
                data: null,
                timeout: 5000,
            });
        });
    });
});
