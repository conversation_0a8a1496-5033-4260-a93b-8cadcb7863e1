import { useMutation, type UseMutationOptions } from '@tanstack/react-query';

import type { ApiError, FetchyResponse } from '../types';

export const useFetchyMutation = <TData, TVariables>(
    mutationFn: (variables: TVariables) => Promise<FetchyResponse<TData>>,
    options?: UseMutationOptions<FetchyResponse<TData>, ApiError, TVariables>,
) => {
    return useMutation({
        mutationFn,
        ...options,
    });
};
