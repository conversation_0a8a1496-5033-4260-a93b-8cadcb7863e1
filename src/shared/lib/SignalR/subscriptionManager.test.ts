import { describe, it, expect, beforeEach, vi } from 'vitest';

import { SubscriptionManager } from './subscriptionManager';
import type { HubEvents, SubscriptionId } from './types';

// Test event types
interface TestEvents extends HubEvents {
    'user:joined': { userId: string; timestamp: number };
    'message:received': { message: string; sender: string };
    'status:changed': { status: 'online' | 'offline' };
}

describe('SubscriptionManager', () => {
    let manager: SubscriptionManager<TestEvents>;
    let mockCallback1: ReturnType<typeof vi.fn>;
    let mockCallback2: ReturnType<typeof vi.fn>;
    let mockCallback3: ReturnType<typeof vi.fn>;

    beforeEach(() => {
        manager = new SubscriptionManager<TestEvents>();
        mockCallback1 = vi.fn();
        mockCallback2 = vi.fn();
        mockCallback3 = vi.fn();
    });

    describe('subscribe', () => {
        it('should create a new subscription and return a unique ID', () => {
            const subscriptionId = manager.subscribe('user:joined', mockCallback1);

            expect(subscriptionId).toBeDefined();
            expect(typeof subscriptionId).toBe('string');
            expect(subscriptionId.length).toBeGreaterThan(0);
        });

        it('should allow multiple subscriptions to the same event', () => {
            const id1 = manager.subscribe('user:joined', mockCallback1);
            const id2 = manager.subscribe('user:joined', mockCallback2);

            expect(id1).not.toBe(id2);
        });

        it('should allow subscriptions to different events', () => {
            const id1 = manager.subscribe('user:joined', mockCallback1);
            const id2 = manager.subscribe('message:received', mockCallback2);

            expect(id1).not.toBe(id2);
        });

        it('should generate unique IDs for each subscription', () => {
            const ids = new Set<SubscriptionId>();

            for (let i = 0; i < 10; i++) {
                const id = manager.subscribe('user:joined', vi.fn());
                ids.add(id);
            }

            expect(ids.size).toBe(10);
        });
    });

    describe('unsubscribe', () => {
        it('should successfully unsubscribe an existing subscription', () => {
            const subscriptionId = manager.subscribe('user:joined', mockCallback1);
            const result = manager.unsubscribe('user:joined', subscriptionId);

            expect(result).toBe(true);
        });

        it('should return false when unsubscribing non-existent subscription', () => {
            const result = manager.unsubscribe('user:joined', 'non-existent-id');

            expect(result).toBe(false);
        });

        it('should return false when unsubscribing from non-existent event', () => {
            const result = manager.unsubscribe('non-existent-event' as keyof TestEvents, 'some-id');

            expect(result).toBe(false);
        });

        it('should remove event entry when last subscription is removed', () => {
            const subscriptionId = manager.subscribe('user:joined', mockCallback1);
            manager.unsubscribe('user:joined', subscriptionId);

            // Try to unsubscribe again - should return false
            const result = manager.unsubscribe('user:joined', subscriptionId);
            expect(result).toBe(false);
        });

        it('should only remove the specified subscription, not others', () => {
            const id1 = manager.subscribe('user:joined', mockCallback1);
            const id2 = manager.subscribe('user:joined', mockCallback2);

            manager.unsubscribe('user:joined', id1);

            // Second subscription should still exist
            const result = manager.unsubscribe('user:joined', id2);
            expect(result).toBe(true);
        });
    });

    describe('broadcast', () => {
        it('should call all subscribers for an event', () => {
            const testData = { userId: '123', timestamp: Date.now() };

            manager.subscribe('user:joined', mockCallback1);
            manager.subscribe('user:joined', mockCallback2);

            manager.broadcast('user:joined', testData);

            expect(mockCallback1).toHaveBeenCalledWith(testData);
            expect(mockCallback2).toHaveBeenCalledWith(testData);
        });

        it('should not call subscribers for other events', () => {
            const testData = { userId: '123', timestamp: Date.now() };

            manager.subscribe('user:joined', mockCallback1);
            manager.subscribe('message:received', mockCallback2);

            manager.broadcast('user:joined', testData);

            expect(mockCallback1).toHaveBeenCalledWith(testData);
            expect(mockCallback2).not.toHaveBeenCalled();
        });

        it('should handle broadcast to non-existent event gracefully', () => {
            expect(() => {
                manager.broadcast('user:joined', { userId: '123', timestamp: Date.now() });
            }).not.toThrow();
        });

        it('should handle broadcast to event with no subscribers gracefully', () => {
            expect(() => {
                manager.broadcast('user:joined', { userId: '123', timestamp: Date.now() });
            }).not.toThrow();
        });

        it('should call callbacks with correct data types', () => {
            const userData = { userId: '123', timestamp: Date.now() };
            const messageData = { message: 'Hello', sender: 'user1' };
            const statusData = { status: 'online' as const };

            manager.subscribe('user:joined', mockCallback1);
            manager.subscribe('message:received', mockCallback2);
            manager.subscribe('status:changed', mockCallback3);

            manager.broadcast('user:joined', userData);
            manager.broadcast('message:received', messageData);
            manager.broadcast('status:changed', statusData);

            expect(mockCallback1).toHaveBeenCalledWith(userData);
            expect(mockCallback2).toHaveBeenCalledWith(messageData);
            expect(mockCallback3).toHaveBeenCalledWith(statusData);
        });
    });

    describe('unsubscribeAll', () => {
        it('should remove all subscriptions for an event and return count', () => {
            manager.subscribe('user:joined', mockCallback1);
            manager.subscribe('user:joined', mockCallback2);
            manager.subscribe('message:received', mockCallback3);

            const removedCount = manager.unsubscribeAll('user:joined');

            expect(removedCount).toBe(2);
        });

        it('should return 0 for non-existent event', () => {
            const removedCount = manager.unsubscribeAll('user:joined');

            expect(removedCount).toBe(0);
        });

        it('should remove event entry after unsubscribeAll', () => {
            manager.subscribe('user:joined', mockCallback1);
            manager.unsubscribeAll('user:joined');

            // Try to unsubscribe a specific subscription - should return false
            const result = manager.unsubscribe('user:joined', 'some-id');
            expect(result).toBe(false);
        });

        it('should not affect other events when unsubscribing all from one event', () => {
            manager.subscribe('user:joined', mockCallback1);
            manager.subscribe('message:received', mockCallback2);

            manager.unsubscribeAll('user:joined');

            // Other event should still have subscribers
            manager.broadcast('message:received', { message: 'test', sender: 'user' });
            expect(mockCallback2).toHaveBeenCalled();
        });
    });

    describe('dispose', () => {
        it('should clear all subscriptions', () => {
            manager.subscribe('user:joined', mockCallback1);
            manager.subscribe('message:received', mockCallback2);

            manager.dispose();

            // Try to broadcast - should not call any callbacks
            manager.broadcast('user:joined', { userId: '123', timestamp: Date.now() });
            manager.broadcast('message:received', { message: 'test', sender: 'user' });

            expect(mockCallback1).not.toHaveBeenCalled();
            expect(mockCallback2).not.toHaveBeenCalled();
        });

        it('should allow new subscriptions after dispose', () => {
            manager.subscribe('user:joined', mockCallback1);
            manager.dispose();

            // Should be able to subscribe again
            const newId = manager.subscribe('user:joined', mockCallback2);
            expect(newId).toBeDefined();

            manager.broadcast('user:joined', { userId: '123', timestamp: Date.now() });
            expect(mockCallback2).toHaveBeenCalled();
        });
    });

    describe('integration scenarios', () => {
        it('should handle complex subscription lifecycle', () => {
            // Subscribe to multiple events
            const id1 = manager.subscribe('user:joined', mockCallback1);

            // Broadcast to first event
            const userData = { userId: '123', timestamp: Date.now() };
            manager.broadcast('user:joined', userData);

            expect(mockCallback1).toHaveBeenCalledWith(userData);
            expect(mockCallback2).toHaveBeenCalledWith(userData);
            expect(mockCallback3).not.toHaveBeenCalled();

            // Unsubscribe one subscription
            manager.unsubscribe('user:joined', id1);

            // Broadcast again
            const userData2 = { userId: '456', timestamp: Date.now() };
            manager.broadcast('user:joined', userData2);

            expect(mockCallback1).toHaveBeenCalledTimes(1); // Should not be called again
            expect(mockCallback2).toHaveBeenCalledWith(userData2);

            // Unsubscribe all from first event
            const removedCount = manager.unsubscribeAll('user:joined');
            expect(removedCount).toBe(1);

            // Broadcast to second event
            const messageData = { message: 'Hello', sender: 'user' };
            manager.broadcast('message:received', messageData);

            expect(mockCallback3).toHaveBeenCalledWith(messageData);
        });

        it('should handle rapid subscribe/unsubscribe operations', () => {
            const ids: SubscriptionId[] = [];

            // Rapidly subscribe and unsubscribe
            for (let i = 0; i < 5; i++) {
                const id = manager.subscribe('user:joined', vi.fn());
                ids.push(id);
                manager.unsubscribe('user:joined', id);
            }

            // Should not have any active subscriptions
            manager.broadcast('user:joined', { userId: '123', timestamp: Date.now() });
            // No assertions needed - just ensuring no errors occur
        });

        it('should handle multiple events with same callback', () => {
            const sharedCallback = vi.fn();

            manager.subscribe('user:joined', sharedCallback);
            manager.subscribe('message:received', sharedCallback);

            const userData = { userId: '123', timestamp: Date.now() };
            const messageData = { message: 'Hello', sender: 'user' };

            manager.broadcast('user:joined', userData);
            manager.broadcast('message:received', messageData);

            expect(sharedCallback).toHaveBeenCalledWith(userData);
            expect(sharedCallback).toHaveBeenCalledWith(messageData);
            expect(sharedCallback).toHaveBeenCalledTimes(2);
        });
    });

    describe('edge cases', () => {
        it('should handle empty callback functions', () => {
            const emptyCallback = () => {};
            const subscriptionId = manager.subscribe('user:joined', emptyCallback);

            expect(() => {
                manager.broadcast('user:joined', { userId: '123', timestamp: Date.now() });
            }).not.toThrow();

            expect(manager.unsubscribe('user:joined', subscriptionId)).toBe(true);
        });

        it('should propagate errors from callbacks', () => {
            const throwingCallback = vi.fn().mockImplementation(() => {
                throw new Error('Callback error');
            });

            manager.subscribe('user:joined', throwingCallback);

            // Errors in callbacks should propagate to the caller
            expect(() => {
                manager.broadcast('user:joined', { userId: '123', timestamp: Date.now() });
            }).toThrow('Callback error');
        });

        it('should handle unsubscribe of already unsubscribed subscription', () => {
            const subscriptionId = manager.subscribe('user:joined', mockCallback1);
            manager.unsubscribe('user:joined', subscriptionId);

            // Try to unsubscribe again
            const result = manager.unsubscribe('user:joined', subscriptionId);
            expect(result).toBe(false);
        });

        it('should handle dispose when no subscriptions exist', () => {
            expect(() => {
                manager.dispose();
            }).not.toThrow();
        });

        it('should handle unsubscribeAll when no subscriptions exist for event', () => {
            const result = manager.unsubscribeAll('user:joined');
            expect(result).toBe(0);
        });
    });
});
