/* eslint-disable @typescript-eslint/no-explicit-any */
export interface SignalrConfig {
    hubUrl: string;
    automaticReconnect: boolean;
    reconnectDelays: number[];
    enableMessagePack: boolean;
    onConnectionStateChange?: (state: ConnectionState) => void;
    retryAttempts?: number;
    retryDelay?: number;
}

export type ConnectionState = 'connected' | 'disconnected' | 'connecting' | 'reconnecting';
export type SubscriptionId = string;

export type Subscription = Map<SubscriptionId, (data: any) => void>;

export type EventName = string;
export type EventData = any;
export type HubEvents = Record<EventName, EventData>;
export type GroupName = string;
