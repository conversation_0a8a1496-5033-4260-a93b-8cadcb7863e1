/* eslint-disable @typescript-eslint/no-explicit-any */

import type { HubEvents, Subscription, SubscriptionId } from './types';

export class SubscriptionManager<T extends HubEvents> {
    private subscribers: Map<keyof T, Subscription> = new Map();

    public subscribe<K extends keyof T>(eventName: K, callback: (data: T[K]) => void): SubscriptionId {
        if (!this.subscribers.has(eventName)) {
            this.subscribers.set(eventName, new Map());
        }
        const subscriberId = crypto.randomUUID();
        this.subscribers.get(eventName)?.set(subscriberId, callback as (data: any) => void);
        return subscriberId;
    }

    public unsubscribe<K extends keyof T>(eventName: K, subscriberId: SubscriptionId): boolean {
        const eventSubscribers = this.subscribers.get(eventName);
        if (eventSubscribers) {
            const deleted = eventSubscribers.delete(subscriberId);
            if (eventSubscribers.size === 0) {
                this.subscribers.delete(eventName);
            }
            return deleted;
        }
        return false;
    }

    public broadcast<K extends keyof T>(eventName: K, data: T[K]): void {
        this.subscribers.get(eventName)?.forEach((callback) => callback(data));
    }

    public unsubscribeAll<K extends keyof T>(eventName: K): number {
        const eventSubscribers = this.subscribers.get(eventName);
        if (eventSubscribers) {
            const count = eventSubscribers.size;
            eventSubscribers.clear();
            this.subscribers.delete(eventName);
            return count;
        }
        return 0;
    }

    public dispose(): void {
        this.subscribers.clear();
    }
}
