import type { HubConnection } from '@microsoft/signalr';
import { HubConnectionBuilder, LogLevel } from '@microsoft/signalr';
import { MessagePackHubProtocol } from '@microsoft/signalr-protocol-msgpack';

import { logger } from '@/infrastructure/logging';

import type { ConnectionState, SignalrConfig } from './types';

export class SignalrHubManager {
    private connection: HubConnection | null = null;
    private config: SignalrConfig;
    private connectionState: ConnectionState = 'disconnected';

    private defaultConfig: SignalrConfig = {
        hubUrl: '',
        automaticReconnect: true,
        reconnectDelays: [0, 2000, 10000, 30000],
        enableMessagePack: false,
        retryAttempts: 3,
        retryDelay: 1000,
    };

    constructor(config: Partial<SignalrConfig> = {}) {
        this.config = { ...this.defaultConfig, ...config };
    }

    public async connect(eventHandlers: (hub: HubConnection) => void): Promise<void> {
        if (this.connection?.state === 'Connected') {
            logger.warn('SignalrHubManager: Already connected');
            return;
        }

        try {
            this.connectionState = 'connecting';
            this.connection = this.build(this.config);

            eventHandlers(this.connection);
            this.setupConnectionHandlers();

            await this.connection.start();
            this.connectionState = 'connected';
            this.config.onConnectionStateChange?.(this.connectionState);

            logger.info('SignalrHubManager: Connected to .NET SignalR Hub', {
                hubUrl: this.config.hubUrl,
            });
        } catch (error) {
            this.connectionState = 'disconnected';
            this.config.onConnectionStateChange?.(this.connectionState);
            logger.error('SignalrHubManager: Connection failed', error as Error);
            throw error;
        }
    }

    public async sendToHub(methodName: string, ...args: unknown[]): Promise<unknown> {
        if (!this.connection || this.connection.state !== 'Connected') {
            throw new Error('SignalR connection not available');
        }

        let lastError: Error | undefined;
        const maxAttempts = this.config.retryAttempts || 3;

        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                logger.debug('SignalrHubManager: Sending to hub', { methodName, args, attempt });
                return await this.connection.invoke(methodName, ...args);
            } catch (error) {
                lastError = error as Error;
                logger.warn('SignalrHubManager: Send attempt failed', {
                    methodName,
                    attempt,
                    maxAttempts,
                    error: lastError.message,
                });

                if (attempt < maxAttempts) {
                    const delay = this.config.retryDelay || 1000;
                    await new Promise((resolve) => setTimeout(resolve, delay));
                }
            }
        }

        logger.error('SignalrHubManager: All send attempts failed', lastError as Error, {
            methodName,
            attempts: maxAttempts,
        });
        throw lastError;
    }

    public getConnectionState(): ConnectionState {
        return this.connectionState;
    }

    public async disconnect(): Promise<void> {
        if (this.connection) {
            await this.connection.stop();
            this.connection = null;
            this.connectionState = 'disconnected';
            this.config.onConnectionStateChange?.(this.connectionState);

            logger.info('SignalrHubManager: Disconnected');
        }
    }

    private build(config: SignalrConfig): HubConnection {
        const builder = new HubConnectionBuilder()
            .withUrl(config.hubUrl, {
                withCredentials: false,
            })
            .withAutomaticReconnect(config.reconnectDelays)
            .configureLogging(LogLevel.Error);

        // Add MessagePack if enabled (better performance)
        if (config.enableMessagePack) {
            builder.withHubProtocol(new MessagePackHubProtocol());
            logger.warn('SignalrHubManager: MessagePack enabled ');
        }

        return builder.build();
    }

    private setupConnectionHandlers(): void {
        if (!this.connection) return;

        this.connection.onreconnecting((error) => {
            this.connectionState = 'reconnecting';
            this.config.onConnectionStateChange?.(this.connectionState);
            logger.warn('SignalrHubManager: Reconnecting...', { error: error?.message });
        });

        this.connection.onreconnected((connectionId) => {
            this.connectionState = 'connected';
            this.config.onConnectionStateChange?.(this.connectionState);
            logger.info('SignalrHubManager: Reconnected', { connectionId });
        });

        this.connection.onclose((error) => {
            this.connectionState = 'disconnected';
            this.config.onConnectionStateChange?.(this.connectionState);
            logger.warn('SignalrHubManager: Connection closed', { error: error?.message });
        });
    }
}
