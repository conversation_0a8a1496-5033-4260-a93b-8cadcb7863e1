import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

/**
 * Type definition for the localized function returned by useLocalized.
 *
 * A `LocalizedFn` is both:
 * - A callable function that takes a dictionary of translations and returns the best match.
 * - An object with two additional properties:
 *   - `localized`: A method reference to itself (to allow usage in both function-call and object-destructuring styles).
 *   - `dir`: A string representing the text direction (`'rtl'` for Arabic, `'ltr'` otherwise).
 */
type LocalizedFn = {
    (obj: Record<string, string>): string;
    localized: (obj: Record<string, string>) => string;
    dir: 'rtl' | 'ltr';
};

/**
 * React hook: useLocalized
 * ------------------------
 * Provides a simple way to select localized text objects based on the active language
 * in the `react-i18next` configuration.
 *
 * ### Features
 * - **Callable**: You can call it directly as a function:
 *   ```ts
 *   const localized = useLocalized();
 *   const label = localized({ english: "Hello", arabic: "مرحبا" });
 *   ```
 *
 * - **Destructurable**: You can also destructure it to access `localized` and `dir`:
 *   ```ts
 *   const { localized, dir } = useLocalized();
 *   const label = localized({ english: "Hello", arabic: "مرحبا" });
 *   ```
 *
 * - **Fallback support**: If the active language key is missing, it falls back to a provided default (`english` by default).
 *
 * ### Parameters
 * - `fallback`: The default language to fall back to (`'arabic' | 'english' | ''`).
 *
 * ### Returns
 * A function (`LocalizedFn`) that selects the correct localized string, with:
 * - `.localized`: A direct method reference to itself.
 * - `.dir`: `'rtl'` if Arabic is active, otherwise `'ltr'`.
 */
export function useLocalized(fallback: 'arabic' | 'english' | '' = 'english'): LocalizedFn {
    const { i18n } = useTranslation();

    // Memoized function that resolves the correct localized string.
    const localized = useCallback(
        (obj: Record<string, string>) => {
            const lang = i18n.language === 'ar' ? 'arabic' : 'english';
            return obj?.[lang] ?? obj?.[fallback] ?? '';
        },
        [i18n.language, fallback],
    ) as LocalizedFn;

    // Attach extra properties to allow destructuring usage.
    localized.localized = localized;
    localized.dir = i18n.language === 'ar' ? 'rtl' : 'ltr';

    return localized;
}
