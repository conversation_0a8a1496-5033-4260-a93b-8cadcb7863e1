import { format } from 'date-fns';
import { enUS, ar } from 'date-fns/locale';

import i18n from '../config/i18n.config';

export function formatLocalizedDate(dateValue: Date | string | undefined): string {
    if (!dateValue) return '-';

    const d = typeof dateValue === 'string' ? new Date(dateValue) : dateValue;

    const isArabic = i18n.language === 'ar';
    const locale = isArabic ? ar : enUS;

    // Time format differs for LTR/RTL
    const time = isArabic
        ? format(d, 'a hh:mm:ss', { locale }) // RTL Arabic
        : format(d, 'hh:mm:ss a', { locale: enUS }); // LTR English

    const date = format(d, 'dd/MM/yyyy', { locale });

    // Return string with correct order
    return isArabic ? `${time} - ${date}` : `${date} - ${time}`;
}
