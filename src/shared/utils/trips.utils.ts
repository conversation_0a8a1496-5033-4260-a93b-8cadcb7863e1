import type { IconName } from '@/components/common/ui/Icon';
import type { TripState } from '@/infrastructure/api/trips/types';

export type TripStates = {
    id: string; // ✅ stable key
    icon: IconName;
    value: string | number | boolean;
    textColor: string;
    bgColor: string;
    tooltipKey: string;
};

export function constructTripStates(tripState: TripState | null): TripStates[] {
    if (tripState === null || tripState === undefined) return [];

    const conditions: TripStates[] = [
        {
            id: 'gps-signal',
            icon:
                tripState.gpsSignalStrength > 3
                    ? 'gpsSignalFull'
                    : tripState.gpsSignalStrength > 1
                      ? 'gpsSignalWeak'
                      : 'gpsDisconnected',
            value: `${tripState.gpsSignalStrength}%`,
            textColor: tripState.gpsSignalStrength > 2 ? 'text-green-700' : 'text-red-700',
            bgColor: tripState.gpsSignalStrength > 2 ? 'bg-green-100' : 'bg-red-100',
            tooltipKey: 'tripDetails.statusConditions.gpsSignalStrength',
        },
        {
            id: 'gsm-signal',
            icon:
                tripState.gsmSignalStrength >= 3
                    ? 'signalFull'
                    : tripState.gsmSignalStrength === 2
                      ? 'signalMedium'
                      : tripState.gsmSignalStrength === 1
                        ? 'signalLow'
                        : 'signalNo',

            value: `${tripState.gsmSignalStrength}%`,
            textColor: tripState.gsmSignalStrength > 2 ? 'text-green-700' : 'text-red-700',
            bgColor: tripState.gsmSignalStrength > 2 ? 'bg-green-100' : 'bg-red-100',
            tooltipKey: 'tripDetails.statusConditions.gsmSignalStrength',
        },
        {
            id: 'battery',
            icon:
                tripState.batteryLevelPercentage > 80
                    ? 'batteryFull'
                    : tripState.batteryLevelPercentage > 50
                      ? 'batteryMedium'
                      : tripState.batteryLevelPercentage > 20
                        ? 'batteryLow'
                        : tripState.batteryLevelPercentage > 5
                          ? 'batteryVeryLow'
                          : 'batteryEmpty',
            value: `${tripState.batteryLevelPercentage}%`,
            textColor:
                tripState.batteryLevelPercentage > 50
                    ? 'text-green-700'
                    : tripState.batteryLevelPercentage > 20
                      ? 'text-yellow-700'
                      : 'text-red-700',
            bgColor:
                tripState.batteryLevelPercentage > 50
                    ? 'bg-green-100'
                    : tripState.batteryLevelPercentage > 20
                      ? 'bg-yellow-100'
                      : 'bg-red-100',
            tooltipKey: 'tripDetails.statusConditions.batteryLevel',
        },
        {
            id: 'speed',
            icon: 'speed',
            value: `${tripState.currentSpeed} km/h`,
            textColor: tripState.currentSpeed > 0 ? 'text-blue-700' : 'text-gray-700',
            bgColor: tripState.currentSpeed > 0 ? 'bg-blue-100' : 'bg-gray-100',
            tooltipKey: 'tripDetails.statusConditions.currentSpeed',
        },
        {
            id: 'zone',
            icon: 'alert',
            value: tripState.isWithinSuspiciousZone ? 'Suspicious' : 'Normal',
            textColor: tripState.isWithinSuspiciousZone ? 'text-red-700' : 'text-green-700',
            bgColor: tripState.isWithinSuspiciousZone ? 'bg-red-100' : 'bg-green-100',
            tooltipKey: 'tripDetails.statusConditions.suspiciousZone',
        },
        {
            id: 'route-geofence',
            icon: tripState.isWithinRouteGeofence ? 'enteringGeoFence' : 'leavingGeoFence',
            value: tripState.isWithinRouteGeofence ? 'On Route' : 'Off Route',
            textColor: tripState.isWithinRouteGeofence ? 'text-green-700' : 'text-red-700',
            bgColor: tripState.isWithinRouteGeofence ? 'bg-green-100' : 'bg-red-100',
            tooltipKey: 'tripDetails.statusConditions.routeGeofence',
        },
        {
            id: 'trip-time',
            icon: 'tripPin', // 🕒 closest trip-related icon
            value: `${tripState.timeElapsedSinceTripStartInMinutes} min`,
            textColor: 'text-gray-700',
            bgColor: 'bg-gray-100',
            tooltipKey: 'tripDetails.statusConditions.timeElapsedSinceTripStart',
        },
        {
            id: 'remaining-distance',
            icon: tripState.remainingDistanceInMeters > 0 ? 'truck' : 'checkPointPin',
            value:
                tripState.remainingDistanceInMeters > 0
                    ? `${(tripState.remainingDistanceInMeters / 1000).toFixed(1)} km`
                    : 'Arrived',
            textColor: tripState.remainingDistanceInMeters > 0 ? 'text-blue-700' : 'text-green-700',
            bgColor: tripState.remainingDistanceInMeters > 0 ? 'bg-blue-100' : 'bg-green-100',
            tooltipKey: 'tripDetails.statusConditions.remainingDistance',
        },
    ];

    return conditions;
}
