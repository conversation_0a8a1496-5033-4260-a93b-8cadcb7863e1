import type { IconName } from '@/components/common/ui/Icon';

export const alertIconMapping: Record<number, IconName> = {
    10000: 'gpsSignalWeak',
    10001: 'gpsDisconnected',
    10002: 'gpsDisconnected',
    10003: 'gpsDisconnected',
    10004: 'gpsDisconnected',
    10005: 'batteryVeryLow',
    10006: 'batteryVeryLow',
    10007: 'batteryVeryLow',
    10008: 'signalNo',
    10009: 'signalNo',
    10010: 'signalLow',
    10011: 'signalLow',
    10012: 'tripPin',
    10013: 'truck',
    10014: 'batteryLow',
    10015: 'batteryLow',
    10016: 'gpsDisconnected',
    10017: 'gpsDisconnected',
    10018: 'gpsDisconnected',
    10019: 'gpsDisconnected',
    10020: 'gpsDisconnected',
    10021: 'gpsDisconnected',
    10022: 'gpsDisconnected',
    10023: 'gpsDisconnected',
    10024: 'gpsDisconnected',
    10025: 'gpsDisconnected',
    10040: 'gpsDisconnected',
    10060: 'gpsDisconnected',
    10061: 'gpsDisconnected',
    10062: 'gpsDisconnected',
    10063: 'gpsDisconnected',
    10066: 'gpsDisconnected',
    10067: 'gpsDisconnected',
    10080: 'gpsDisconnected',
    10101: 'gpsDisconnected',
};

/*



2	العبث بجهاز التتبع
3	سقوط جھاز التتبع
4	العبث في القفل
5	تم فتح القفل
6	فقدان الإتصال مع القفل
7	بطارية جھاز التتبع ضعيفة
8	بطارية القفل ضعيفة
9	بطارية القفل ضعيفة
10	فقدان إشارة شبكة الإتصال
11	فقدان إشارة تحديد الموقع
12	تحذير – التحقق من حالة القفل
13	تحذير – التحقق من حالة جھاز التتبع
14	تفعيل الرحلة
15	إغلاق الرحلة
16	حالة بطارية جھاز التتبع
17	تغير حالة الشاحن
18	حالة إشارة الإقمار الصناعية
19	حالة إشارة شبكة الإتصال
20	عدد مرات فتح القفل
21	معلومات تشخيص القفل
22	معلومات تشخيص جھاز التتبع
23	دخول السياج الجغرافي
24	الخروج من السياج الجغرافي
25	لايمكن الاتصال بجهاز التتبع
26	تعدي مسافة الرحلة
27	تعدي وقت الرحلة
28	تعدي السرعة
29	الشاحنة متوقفة
30	عكس السير
31	عدد رسائل القفل
32	تعديل فترة الإرسال
33	منطقة اشتباه
34	الشاحنة تحركت
35	توقف 4 ساعات
36	دخول منطقة الجمارك

*/
