@import 'tailwindcss';
@import 'tw-animate-css';
@import './assets/styles/fonts.css';
@import './assets/styles/tokens.css';

@layer base {
    * {
        @apply border-border outline-ring/50;
    }

    body {
        @apply font-family-sans bg-background text-foreground;
        font-weight: 400;

        margin: 0;
        background-color: var(--bg-color);
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        height: 100dvh;
        overflow: hidden;
    }
}

._effect {
    box-shadow: 0px 10px 22px #33333315;
    border: 1px solid #f0f0f0;
}

/* Notes: her 'position: sticky' will not work until you add (mak-height | max-h-[xx]) to the html element that contain the table inside */
._table_container {
    padding-inline: 10px;
    /* max-height: 100%; */
    overflow-y: auto;

    table {
        border-radius: 8px;
        position: relative;
        width: 100%;

        th,
        td {
            text-wrap: balance;
            padding: 3px 8px;
            font-size: 14px;
            text-align: center;
        }

        th {
            /* background-color: #aaaaaa34; */
            background-color: #eeeeee;
            font-size: 14px;
            font-weight: 600;
            padding: 11px 1rem;
            position: sticky;
            top: 0;
            z-index: 1;
            padding: 10px;
        }

        th * {
            flex: 1;
            justify-content: center;
        }

        tr:nth-child(even) td {
            background: #aaaaaa18;
        }
    }
}

/* ------------- Scrollbar Section (lightweight Scroll) --------------- */

._scrollable::-webkit-scrollbar {
    width: 5px;
}

.scrollable::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px grey;
    border-radius: 10px;
}

._scrollable::-webkit-scrollbar-thumb {
    background: red;
    border-radius: 10px;
}

input:not([type='checkbox'], [type='radio']) {
    border-radius: 5px;
    padding: 9px;
    box-shadow: none;
    width: 100%;
    font-size: 15px;
    transition: 0.3s;
}

button {
    cursor: pointer;
}

::placeholder {
    color: #aaa;
}

#root {
    height: 100%;
}

/* ------ Paginator Overriding ------ */
.p-paginator {
    direction: ltr;
    padding: 0px;
    border: none;
}

.p-paginator * {
    font-size: 13px;
}

.p-paginator .p-paginator-prev,
.p-paginator .p-paginator-first,
.p-paginator .p-dropdown,
.p-dropdown-panel .p-dropdown-items .p-dropdown-item,
.p-paginator .p-paginator-pages .p-paginator-page {
    height: 2.5rem;
}

/* --------------------------- */

@custom-variant dark (&:is(.dark *));

@theme inline {
    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

:root {
    --radius: 0.625rem;
    --background: oklch(1 0 0);
    --foreground: oklch(0.145 0 0);
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.145 0 0);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.145 0 0);
    --primary: blue;
    --primary-foreground: oklch(0.985 0 0);
    --secondary: oklch(0.97 0 0);
    --secondary-foreground: oklch(0.205 0 0);
    --muted: oklch(0.97 0 0);
    --muted-foreground: oklch(0.556 0 0);
    --accent: oklch(0.97 0 0);
    --accent-foreground: oklch(0.205 0 0);
    --destructive: oklch(0.577 0.245 27.325);
    --border: oklch(0.922 0 0);
    --input: oklch(0.922 0 0);
    --ring: oklch(0.708 0 0);
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);
    --sidebar: oklch(0.985 0 0);
    --sidebar-foreground: oklch(0.145 0 0);
    --sidebar-primary: oklch(0.205 0 0);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.97 0 0);
    --sidebar-accent-foreground: oklch(0.205 0 0);
    --sidebar-border: oklch(0.922 0 0);
    --sidebar-ring: oklch(0.708 0 0);
}

.dark {
    --background: oklch(0.145 0 0);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.205 0 0);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.205 0 0);
    --popover-foreground: oklch(0.985 0 0);
    --primary: oklch(0.922 0 0);
    --primary-foreground: oklch(0.205 0 0);
    --secondary: oklch(0.269 0 0);
    --secondary-foreground: oklch(0.985 0 0);
    --muted: oklch(0.269 0 0);
    --muted-foreground: oklch(0.708 0 0);
    --accent: oklch(0.269 0 0);
    --accent-foreground: oklch(0.985 0 0);
    --destructive: oklch(0.704 0.191 22.216);
    --border: oklch(1 0 0 / 10%);
    --input: oklch(1 0 0 / 15%);
    --ring: oklch(0.556 0 0);
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.488 0.243 264.376);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.269 0 0);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(1 0 0 / 10%);
    --sidebar-ring: oklch(0.556 0 0);
}
