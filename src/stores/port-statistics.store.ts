import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';
import { portStatisticsService } from '@/infrastructure/api/ports/statistics/port-statistics.service';
import type { PortStatisticsRequest, PortStatisticsResponse } from '@/infrastructure/api/ports/statistics/types';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------
export type PortStatisticsStoreType = PortStatisticsState & PortStatisticsActions;

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------
interface PortStatisticsState {
    statistics: PortStatisticsResponse | null;
    isLoading: boolean;
    error: string | null;
    lastFetchedPortId: number | null;
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------
const DEFAULT_STATE: PortStatisticsState = {
    statistics: null,
    isLoading: false,
    error: null,
    lastFetchedPortId: null,
};

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------
interface PortStatisticsActions {
    loadPortStatistics: (params: PortStatisticsRequest) => Promise<void>;
    clearStatistics: () => void;
    clearError: () => void;
}

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------
const _portStatisticsStore = (instanceId: string): StateCreator<PortStatisticsStoreType> => {
    return (set, get): PortStatisticsStoreType => ({
        ...DEFAULT_STATE,

        async loadPortStatistics(params: PortStatisticsRequest) {
            const { lastFetchedPortId } = get();

            // Avoid unnecessary API calls for the same port
            if (lastFetchedPortId === params.portId && get().statistics && !get().error) {
                logger.info(`${instanceId}: Port statistics already loaded for port ${params.portId}`);
                return;
            }

            set({ isLoading: true, error: null });

            try {
                const response = await portStatisticsService.getPortStatistics(params);

                if (response === null) {
                    set({
                        statistics: null,
                        isLoading: false,
                        error: 'Port not found or failed to load statistics',
                        lastFetchedPortId: params.portId,
                    });
                    return;
                }

                set({
                    statistics: response,
                    isLoading: false,
                    error: null,
                    lastFetchedPortId: params.portId,
                });

                logger.info(`${instanceId}: loadPortStatistics success`, { params, response });
            } catch (error: unknown) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
                logger.error(`${instanceId}: loadPortStatistics error`, error as Error);
                set({
                    statistics: null,
                    isLoading: false,
                    error: errorMessage,
                    lastFetchedPortId: params.portId,
                });
            }
        },

        clearStatistics: () => {
            set({ statistics: null, error: null, lastFetchedPortId: null });
            logger.info(`${instanceId}: clearStatistics`);
        },

        clearError: () => {
            set({ error: null });
            logger.info(`${instanceId}: clearError`);
        },
    });
};

// ----------------------------------------------------------
// ---------------------- export store ----------------------
// ----------------------------------------------------------
export const usePortStatisticsStore = create<PortStatisticsStoreType>()(
    subscribeWithSelector(devtools(_portStatisticsStore('main'), { name: 'portStatistics-store' })),
);

// ------------------------------------------------------------
// ---------------------- computed values ----------------------
// ------------------------------------------------------------
export const usePortStatisticsComputed = () => {
    const statistics = usePortStatisticsStore((state) => state.statistics);

    return {
        totalActiveTrips: statistics ? statistics.activeTrips.inbound + statistics.activeTrips.outbound : 0,
        hasData: statistics !== null,
    };
};
