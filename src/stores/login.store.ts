import toast from 'react-hot-toast';
import { type NavigateFunction } from 'react-router-dom';
import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { loginCredentialsSchema, type LoginCredentials } from '@/infrastructure/validation/schemas/login.schema';
import { valy } from '@/shared/lib/Valy';

//---------------- Types ----------------
type LoginCredentialsState = {
    formDetails: LoginCredentials;
};

type Loader = { isDataLoading: boolean };

type LoginActions = {
    setUsername: (username: string) => void;
    setPassword: (password: string) => void;
    submit: (navigate: NavigateFunction) => void;
};

export type LoginStoreType = LoginCredentialsState & Loader & LoginActions;

//---------------- Store ----------------

export const useLoginStore = create<LoginStoreType>()(
    subscribeWithSelector(
        devtools(
            (set) => ({
                formDetails: { username: '', password: '' },
                isDataLoading: false,
                setUsername: (username: string) =>
                    set((state) => ({ formDetails: { ...state.formDetails, username } })),
                setPassword: (password: string) =>
                    set((state) => ({ formDetails: { ...state.formDetails, password } })),
                submit: (navigate: NavigateFunction) => FireSubmit(navigate),
            }),
            { name: 'login-store' },
        ),
    ),
);

//---------------- Factory Functions --------------

const FireSubmit = (navigate: NavigateFunction) => {
    const loginStore = useLoginStore.getState();

    const validLoginForm = valy.validate<LoginCredentials>(
        loginCredentialsSchema,
        loginStore.formDetails,
        'LoginCredentials',
    );

    if (!validLoginForm.success) return;

    fireLoader(true);

    //-- SetTimeout her for simulation purpose
    setTimeout(() => {
        fireLoader(false);
        toast.success('Done Successfully', { duration: 4000 });
        navigate('/my-ports');
    }, 2000);
};

const fireLoader = (isDataLoading: boolean) => {
    useLoginStore.setState({ isDataLoading });
};
