import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { DEFAULT_PAGINATION, type Pagination } from '@/infrastructure/validation/schemas/pagination.schema';
import { alertTypesService } from '@/infrastructure/api/alert-types';
import { logger } from '@/infrastructure/logging';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------
export type AlertType = Awaited<ReturnType<typeof alertTypesService.getAlertTypes>>['data'][number];
export type AlertTypeFilters = Awaited<Parameters<typeof alertTypesService.getAlertTypes>[0]>;
export type AlertTypeStoreType = AlertTypeState & AlertTypeActions;

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------

interface AlertTypeState {
    alertTypes: AlertType[];
    pagination: Pagination;
    isLoading: boolean;
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------
const DEFAULT_STATE: AlertTypeState = {
    alertTypes: [],
    pagination: DEFAULT_PAGINATION,
    isLoading: false,
};

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------
interface AlertTypeActions {
    loadAlertTypes: (options: AlertTypeFilters) => Promise<void>;
    loadMoreAlertTypes: (options: AlertTypeFilters) => Promise<void>;
}

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------
const _alertTypeStore = (instanceId: string): StateCreator<AlertTypeStoreType> => {
    return (set, get): AlertTypeStoreType => ({
        ...DEFAULT_STATE,

        async loadAlertTypes(options: AlertTypeFilters = { PageNumber: 1, PageSize: 1000 }) {
            set({ isLoading: true });
            try {
                const response = await alertTypesService.getAlertTypes(options);
                logger.info(`${instanceId}: loadAlertTypes updating state`, { response });

                set(() => ({
                    alertTypes: response.data,
                    pagination: response.pagination,
                    isLoading: false,
                }));
            } catch (error: unknown) {
                logger.error(`${instanceId}: loadAlertTypes error`, error as Error);
                set({ isLoading: false });
            }
        },

        async loadMoreAlertTypes(options: AlertTypeFilters = {}) {
            set({ isLoading: true });
            try {
                options.PageNumber = options.PageNumber ?? get().pagination.currentPage + 1;

                const response = await alertTypesService.getAlertTypes(options);
                logger.info(`${instanceId}: loadMoreAlertTypes updating state`, { response });

                set((state: AlertTypeState) => ({
                    alertTypes:
                        response.pagination.currentPage > 1 ? [...state.alertTypes, ...response.data] : response.data,
                    pagination: response.pagination,
                    isLoading: false,
                }));
            } catch (error: unknown) {
                logger.error(`${instanceId}: loadMoreAlertTypes error`, error as Error);
                set({ isLoading: false });
            }
        },
    });
};

// ----------------------------------------------------------
// ---------------------- export store ----------------------
// ----------------------------------------------------------

export const useAlertTypeStore = create<AlertTypeStoreType>()(
    subscribeWithSelector(devtools(_alertTypeStore('main'), { name: 'alert-type-store' })),
);

// ------------------------------------------------------------
// ---------------------- export lookups ----------------------
// ------------------------------------------------------------
export const useAlertTypeLookups = () =>
    useAlertTypeStore((state) => state.alertTypes).map((item) => ({
        id: item.id,
        name: item.name,
    }));
