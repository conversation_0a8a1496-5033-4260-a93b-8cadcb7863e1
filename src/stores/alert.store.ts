import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';
import type { AlertMessage } from '@/infrastructure/signalr/trip/types';
import { tripHub } from '@/infrastructure/signalr/trip/trip.hub';
import type { GroupName, SubscriptionId } from '@/shared/lib/SignalR';
import { appConfig } from '@/shared/config/app-settings.config';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------

export type Alert = AlertMessage & {
    isAcknowledged: boolean;
};

export type AlertStoreType = AlertState & AlertActions;

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------

interface AlertState {
    alerts: Alert[];
    selectedAlert: Alert | null;
    filteredAlerts: Alert[];
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------

const DEFAULT_STATE: AlertState = {
    alerts: [],
    selectedAlert: null,
    filteredAlerts: [],
};

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------

const subscriptionKeys: Record<GroupName, SubscriptionId> = {};

interface AlertActions {
    acknowledgeAlert: (alert: Alert) => void;
    setIsAcknowledged: (alert: Alert) => void;
    setSelectedAlert: (alert: Alert) => void;
    listenToAlerts: () => void;
    unListenToAlerts: () => void;
    filterAlerts: (filter: (alert: Alert) => boolean) => void;
}

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------
const _alertStore = (instanceId: string): StateCreator<AlertStoreType> => {
    return (set, get): AlertStoreType => {
        // ------------------------------------------------------
        // ---------------------- private actions ---------------
        // ------------------------------------------------------
        const _addAlert = (alert: Alert) => {
            set((state: AlertState) => ({
                ...state,
                alerts: [...state.alerts, alert].slice(-100),
            }));
            logger.info(`${instanceId}: addAlert: alert added`, { alert });
        };

        // ------------------------------------------------------

        return {
            ...DEFAULT_STATE,

            acknowledgeAlert: (alert: Alert) => {
                set((state: AlertState) => ({
                    ...state,
                    alerts: state.alerts.filter((a) => a !== alert),
                }));
                logger.info(`${instanceId}: acknowledgeAlert: alert acknowledged`, { alert });
            },
            setIsAcknowledged: (alert: Alert) => {
                set((state: AlertState) => ({
                    ...state,
                    alerts: state.alerts.map((a) =>
                        a.currentState.id === alert.currentState.id ? { ...a, isAcknowledged: true } : a,
                    ),
                }));
                logger.info(`${instanceId}: setIsAcknowledged: alert marked as acknowledged`, { alert });
            },

            setSelectedAlert: (alert: Alert) => {
                set((state: AlertState) => ({
                    ...state,
                    selectedAlert: alert,
                }));
                logger.info(`${instanceId}: setselectedAlert: alert updated`, { alert });
            },

            listenToAlerts: async () => {
                if (appConfig.get('disableAlertHub')) return;
                await tripHub.connect();
                logger.info(`${instanceId}: listenToAlerts: connecting to hub`);
                tripHub.joinAllTripsGroup();
                logger.info(`${instanceId}: listenToAlerts: joined all trips group`);

                if (subscriptionKeys['ReceiveAllTripAlerts']) {
                    get().unListenToAlerts();
                }

                const subscriptionKey = tripHub.subscribe('ReceiveAllTripAlerts', (alert: AlertMessage) => {
                    logger.info(`${instanceId}: listenToAlerts: alert received`, { alert });

                    _addAlert({ ...alert, isAcknowledged: false });
                });
                logger.info(`${instanceId}: listenToAlerts: subscribed to all trips group`, { subscriptionKey });
                subscriptionKeys['ReceiveAllTripAlerts'] = subscriptionKey;
            },

            unListenToAlerts: () => {
                if (!subscriptionKeys['ReceiveAllTripAlerts']) return;

                tripHub.unsubscribe('ReceiveAllTripAlerts', subscriptionKeys['ReceiveAllTripAlerts']);
                logger.info(`${instanceId}: unListenToAlerts: alert unListened`, {});
            },

            filterAlerts: (filter: (alert: Alert) => boolean) => {
                set((state: AlertState) => ({
                    ...state,
                    filteredAlerts: state.alerts.filter(filter),
                }));
            },
        };
    };
};

// ----------------------------------------------------------
// ---------------------- export store ----------------------
// ----------------------------------------------------------
export const useAlertStore = create<AlertStoreType>()(
    subscribeWithSelector(devtools(_alertStore('main'), { name: 'alert-store' })),
);

// ------------------------------------------------------------
// ---------------------- export lookups ----------------------
// ------------------------------------------------------------
