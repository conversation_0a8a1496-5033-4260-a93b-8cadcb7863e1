import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';
import type { TripsItem, TripsRequest } from '@/infrastructure/api/trips/types';
import { getMapPointIcon } from '@/shared/utils/map.utils';
import { tripsService } from '@/infrastructure/api/trips/trips.service';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------
export type TripsStoreType = TripsState & TripsActions;

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------
interface TripsState {
    trips: TripsItem[];
    isLoading: boolean;
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------
const DEFAULT_STATE: TripsState = {
    trips: [],
    isLoading: false,
};

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------
interface TripsActions {
    loadTrips: (params?: Partial<TripsRequest>) => Promise<void>;
}

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------
const _TripsStore = (instanceId: string): StateCreator<TripsStoreType> => {
    return (set): TripsStoreType => ({
        ...DEFAULT_STATE,

        async loadTrips(params: Partial<TripsRequest> = { pageNumber: 1, pageSize: 1000 }) {
            set({ isLoading: true });
            try {
                const response = await tripsService.getTrips({
                    ...params,
                } as TripsRequest);

                set(() => ({
                    trips: response.data,
                    pagination: response.pagination,
                    isLoading: false,
                }));

                logger.info(`${instanceId}: loadTrips updating state`, { response });
            } catch (error: unknown) {
                logger.error(`${instanceId}: loadTrips error`, error as Error);
                set({ isLoading: false });
            }
        },
    });
};

// ----------------------------------------------------------
// ---------------------- export store ----------------------
// ----------------------------------------------------------
export const useTripsStore = create<TripsStoreType>()(
    subscribeWithSelector(devtools(_TripsStore('main'), { name: 'Trips-store' })),
);

// ------------------------------------------------------------
// ---------------------- export lookups ----------------------
// ------------------------------------------------------------
export const useTripsLookups = () =>
    useTripsStore((state) => state.trips).map((item) => ({
        ...item,
        icon: getMapPointIcon('truck', 'trip'),
    }));
