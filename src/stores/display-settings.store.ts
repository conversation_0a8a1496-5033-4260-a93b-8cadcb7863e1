import { create, type StateCreator } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';

import { LocalStorageKeys } from '@/infrastructure/local-storage/local-storage-keys.constants';
import { logger } from '@/infrastructure/logging';

import {
    type DisplaySettingsStoreType,
    type TripDisplayMode,
    type DisplaySettingsState,
} from '../components/features/monitor-board/menu-taps/display-settings-tap/types';
// Store definition
const _displaySettingsStore = (instanceId: string): StateCreator<DisplaySettingsStoreType> => {
    return (set) => ({
        // settings: {
        //     tripDisplayMode: 'cluster',
        //     checkpoints: [],
        //     ports: [],
        // },
        settings: {
            tripDisplayMode: 'cluster',
            checkpoints: {
                customs: true,
                police: true,
                suspiciousGeofences: true,
            },
            ports: {
                land: true,
                sea: true,
                air: true,
            },
        },

        setTripDisplayMode: (mode: TripDisplayMode) => {
            set((state: DisplaySettingsState) => ({
                settings: { ...state.settings, tripDisplayMode: mode },
            }));
            logger.info('Trip display mode updated', { instanceId, mode });
        },

        toggleCheckpoint: (type) => {
            set((state: DisplaySettingsState) => ({
                settings: {
                    ...state.settings,
                    checkpoints: {
                        ...state.settings.checkpoints,
                        [type]: !state.settings.checkpoints[type],
                    },
                },
            }));
            logger.info('Checkpoint toggled', { instanceId, type });
        },

        togglePort: (type) => {
            set((state: DisplaySettingsState) => ({
                settings: {
                    ...state.settings,
                    ports: {
                        ...state.settings.ports,
                        [type]: !state.settings.ports[type],
                    },
                },
            }));
            logger.info('Port toggled', { instanceId, type });
        },
    });
};

// Exported store hook with Zustand middlewares
export const useDisplaySettingsStore = create<DisplaySettingsStoreType>()(
    subscribeWithSelector(
        devtools(
            persist(_displaySettingsStore('display-settings'), {
                name: LocalStorageKeys.DISPLAY_SETTINGS, // Key used in localStorage
            }),
            { name: 'display-settings-store' }, // Name of the store in the devtools
        ),
    ),
);
