import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';
import { tripsService } from '@/infrastructure/api/trips/trips.service';
import type { TripLocationItem, TripLocationRequest } from '@/infrastructure/api/trips/types';
import { getMapPointIcon } from '@/shared/utils/map.utils';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------
export type TripLocationStoreType = TripLocationState & TripLocationActions;

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------
interface TripLocationState {
    trips: TripLocationItem[];
    isLoading: boolean;
    highlightedTripId: string | null;
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------
const DEFAULT_STATE: TripLocationState = {
    trips: [],
    isLoading: false,
    highlightedTripId: null,
};

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------
interface TripLocationActions {
    loadTripLocations: (params?: Partial<TripLocationRequest>) => Promise<void>;
    setHighlightedTrip: (tripId: string | null) => void;
}

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------
const _tripLocationStore = (instanceId: string): StateCreator<TripLocationStoreType> => {
    return (set): TripLocationStoreType => ({
        ...DEFAULT_STATE,

        async loadTripLocations(params: Partial<TripLocationRequest> = { pageNumber: 1, pageSize: 1000 }) {
            set({ isLoading: true });
            try {
                const response = await tripsService.getTripLocations({
                    ...params,
                } as TripLocationRequest);

                if (response === null) {
                    set({ isLoading: false }); // todo: maybe show error message
                    return;
                }

                set(() => ({
                    trips: response.data,
                    pagination: response.pagination,
                    isLoading: false,
                }));

                logger.info(`${instanceId}: loadTripLocations updating state`, { response });
            } catch (error: unknown) {
                logger.error(`${instanceId}: loadTripLocations error`, error as Error);
                set({ isLoading: false });
            }
        },

        setHighlightedTrip: (tripId) => {
            set({ highlightedTripId: tripId });
            logger.info(`${instanceId}: setHighlightedTrip updated`, { tripId });
        },
    });
};

// ----------------------------------------------------------
// ---------------------- export store ----------------------
// ----------------------------------------------------------
export const useTripLocationStore = create<TripLocationStoreType>()(
    subscribeWithSelector(devtools(_tripLocationStore('main'), { name: 'tripLocation-store' })),
);

// ------------------------------------------------------------
// ---------------------- export lookups ----------------------
// ------------------------------------------------------------
export const useTripLocationsLookups = () =>
    useTripLocationStore((state) => state.trips).map((item) => ({
        ...item,
        icon: getMapPointIcon('truck', 'trip'),
    }));
