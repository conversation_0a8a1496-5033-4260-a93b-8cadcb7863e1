import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { sendEmailSchema, type SendEmail } from '@/infrastructure/validation/schemas/send-email.schema';
import { valy } from '@/shared/lib/Valy';

//---------------- Types ----------------
type SendEmailState = {
    formDetails: SendEmail;
};

type Loader = { isDataLoading: boolean };

type sendEmailActions = {
    setEmail: (email: string) => void;
    setSubject: (subject: string) => void;
    setDescription: (description: string) => void;
    submit: () => void;
};

export type sendEmailStoreType = SendEmailState & Loader & sendEmailActions;

//---------------- Store ----------------

export const useSendEmailStore = create<sendEmailStoreType>()(
    subscribeWithSelector(
        devtools(
            (set) => ({
                formDetails: { email: '', subject: '', description: '' },
                isDataLoading: false,
                setEmail: (email: string) => set((state) => ({ formDetails: { ...state.formDetails, email } })),
                setSubject: (subject: string) => set((state) => ({ formDetails: { ...state.formDetails, subject } })),
                setDescription: (description: string) =>
                    set((state) => ({ formDetails: { ...state.formDetails, description } })),
                submit: () => FireSubmit(),
            }),
            { name: 'sendEmail-store' },
        ),
    ),
);

//---------------- Factory Functions --------------

const FireSubmit = () => {
    const sendEmailStore = useSendEmailStore.getState();

    const validSendEmailForm = valy.validate<SendEmail>(sendEmailSchema, sendEmailStore.formDetails, 'SendEmail');

    if (!validSendEmailForm.success) return;
    // fireLoader(true);
};

// const fireLoader = (isDataLoading: boolean) => {
//    useSendEmailStore.setState({ isDataLoading });
// }
