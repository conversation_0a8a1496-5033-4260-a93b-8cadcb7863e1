import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';
import { portService } from '@/infrastructure/api/ports/port.service';
import type { PortItem, GetPortsQueryParams } from '@/infrastructure/api/ports/types';
import { DEFAULT_PAGINATION, type Pagination } from '@/infrastructure/validation/schemas/pagination.schema';
import { getMapPointIcon } from '@/shared/utils/map.utils';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------
export type PortsStoreType = PortsState & PortsActions;

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------

interface PortsState {
    ports: PortItem[];
    pagination: Pagination;
    isLoading: boolean;
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------
const DEFAULT_STATE: PortsState = {
    ports: [],
    pagination: DEFAULT_PAGINATION,
    isLoading: false,
};

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------
interface PortsActions {
    loadPorts: (params?: Partial<GetPortsQueryParams>) => Promise<void>;
}

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------

const _portsStore = (instanceId: string): StateCreator<PortsStoreType> => {
    return (set): PortsStoreType => ({
        ...DEFAULT_STATE,

        loadPorts: async (params: Partial<GetPortsQueryParams> = { pageNumber: 1, pageSize: 1000 }) => {
            set((state: PortsState) => ({ ...state, isLoading: true }));
            try {
                const res = await portService.getPorts({
                    ...params,
                } as GetPortsQueryParams);

                set((state: PortsState) => ({
                    ...state,
                    ports: res.data,
                    pagination: res.pagination,
                    isLoading: false,
                }));

                logger.info(`portsStore(${instanceId}): loadPorts: status update to `, res);
            } catch (error) {
                logger.error(`portsStore(${instanceId}): loadPorts: error: `, error as Error);
                set((state: PortsState) => ({ ...state, isLoading: false }));
            }
        },
    });
};

// ----------------------------------------------------------
// ---------------------- store instances ---------------------
// ----------------------------------------------------------

// todo: remove this
// Global instance
export const usePortsStore = create<PortsStoreType>()(
    subscribeWithSelector(devtools(_portsStore('global'), { name: 'ports-store-global' })),
);

// Factory function for isolated instances
export const createPortsStore = (instanceId: string) =>
    create<PortsStoreType>()(
        subscribeWithSelector(devtools(_portsStore(instanceId), { name: `ports-store-${instanceId}` })),
    );

// ------------------------------------------------------------
// ---------------------- export lookups ----------------------
// ------------------------------------------------------------

export const usePortsLookups = () =>
    usePortsStore((state) => state.ports).map((item) => ({
        id: item.id,
        name: item.name,
    }));

export const usePortMapMarkers = () =>
    usePortsStore((state) => state.ports).map((item) => ({
        ...item,
        icon: getMapPointIcon(item.type, 'port'),
    }));
