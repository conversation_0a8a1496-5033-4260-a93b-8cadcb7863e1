import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';
import { MapPointService } from '@/infrastructure/api/map-points/map-point.service';
import type { MapPointDataItem, GetMapPointsQueryParams } from '@/infrastructure/api/map-points/types';
import { DEFAULT_PAGINATION, type Pagination } from '@/infrastructure/validation/schemas/pagination.schema';
import { getMapPointIcon } from '@/shared/utils/map.utils';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------
export type MapPointsStoreType = MapPointsState & MapPointsActions;

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------

interface MapPointsState {
    mapPoints: MapPointDataItem[];
    pagination: Pagination;
    isLoading: boolean;
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------
const DEFAULT_STATE: MapPointsState = {
    mapPoints: [],
    pagination: DEFAULT_PAGINATION,
    isLoading: false,
};

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------
interface MapPointsActions {
    loadMapPoints: (params?: Partial<GetMapPointsQueryParams>) => Promise<void>;
}

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------
const mapPointService = MapPointService.getInstance();

const _mapPointsStore = (instanceId: string): StateCreator<MapPointsStoreType> => {
    return (set): MapPointsStoreType => ({
        ...DEFAULT_STATE,

        loadMapPoints: async (params: Partial<GetMapPointsQueryParams> = { pageNumber: 1, pageSize: 1000 }) => {
            set((state: MapPointsState) => ({ ...state, isLoading: true }));
            try {
                const res = await mapPointService.getMapPoints({
                    ...params,
                } as GetMapPointsQueryParams);

                set((state: MapPointsState) => ({
                    ...state,
                    mapPoints: res.data,
                    pagination: res.pagination,
                    isLoading: false,
                }));

                logger.info(`mapPointsStore(${instanceId}): loadMapPoints: status update to `, res);
            } catch (error) {
                logger.error(`mapPointsStore(${instanceId}): loadMapPoints: error: `, error as Error);
                set((state: MapPointsState) => ({ ...state, isLoading: false }));
            }
        },
    });
};

// ----------------------------------------------------------
// ---------------------- export store ----------------------
// ----------------------------------------------------------
export const useMapPointsStore = create<MapPointsStoreType>()(
    subscribeWithSelector(devtools(_mapPointsStore('main'), { name: 'mapPoints-store' })),
);

// ------------------------------------------------------------
// ---------------------- export lookups ----------------------
// ------------------------------------------------------------

export const useMapPointLookups = () =>
    useMapPointsStore((state) => state.mapPoints).map((item) => {
        return {
            ...item,
            icon: getMapPointIcon(item.entryType),
        };
    });
