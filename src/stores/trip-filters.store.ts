import { create, type StateCreator } from 'zustand';
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware';

import { LocalStorageKeys } from '@/infrastructure/local-storage/local-storage-keys.constants';
import { logger } from '@/infrastructure/logging';
import type { TripLocationRequest } from '@/infrastructure/api/trips/types';
import { AlertAcknowledgement } from '@/shared/enums';

// ---------------------------------------------------
// ---------------------- types ----------------------
// ---------------------------------------------------

/** Complete filter shape: TripLocationRequest + extra UI filter state */
export type FiltersShape = TripLocationRequest & {
    useFilters: boolean;
};

/** Sections that share the NamedToggle (ID + boolean) shape */
export type ListSection = 'inPorts' | 'outPorts' | 'alertTypes' | 'tripLocations';

/** Final store type */
export type TripFiltersStoreType = TripFiltersState & TripFiltersActions;

// ---------------------------------------------------
// ---------------------- state ----------------------
// ---------------------------------------------------

/** Store state */
export interface TripFiltersState {
    filters: FiltersShape;
    appliedFiltersCount: number;
}

// -----------------------------------------------------
// ---------------------- actions ----------------------
// -----------------------------------------------------

/** Store actions */
export interface TripFiltersActions {
    setFilters: (filters: FiltersShape) => void;
    updateListSection: (section: ListSection, id: number, value: boolean) => void;
    setFilter: <K extends keyof FiltersShape>(key: K, value: FiltersShape[K]) => void;
    setUseFilters: (value: boolean) => void;
    resetFilters: () => void;
    applyFilters: (callback?: (filters: FiltersShape) => void) => void;
}

// ----------------------------------------------------------
// ---------------------- default state ---------------------
// ----------------------------------------------------------

/** Default filter values */
const createDefaultFilters = (): FiltersShape => ({
    // TripLocationRequest defaults
    inPorts: [],
    outPorts: [],
    alertTypes: [],
    activeAlertsOnly: true,
    transitNumber: null,
    transitSeqNumber: null,
    driverName: null,
    plateNumber: null,
    trackerNumber: null,
    tipCode: null,
    tripCategory: null,
    tripLocations: [],
    activeTripsOnly: null,
    tripStartDate: null,
    tripEndDate: null,
    transDate: null,
    orderBy: null,
    orderDir: null,
    alertAcknowledgement: AlertAcknowledgement.ALL,
    pageSize: 1000,
    pageNumber: 1,
    // Extra filter state
    useFilters: true,
});

// ---------------------------------------------------
// ---------------------- helpers ----------------------
// ---------------------------------------------------

/** Helper: update list by ID toggle */
const getUpdatedList = (list: number[], id: number, checked: boolean) => {
    if (checked) {
        return list.includes(id) ? list : [...list, id];
    }
    return list.filter((x) => x !== id);
};

/**
 * Count the number of applied filters by comparing `filters` with `defaults`.
 * - Excludes pagination/sort keys (pageSize, pageNumber, orderBy, orderDir).
 * - If useFilters === false, returns 0 (no filters applied).
 */
const getAppliedFiltersCount = (
    filters: FiltersShape,
    defaults: FiltersShape,
    excludeKeys: (keyof FiltersShape)[] = ['pageSize', 'pageNumber', 'useFilters'],
): number => {
    let count = 0;

    for (const key in filters) {
        const k = key as keyof FiltersShape;
        if (excludeKeys.includes(k)) continue;

        const value = filters[k];
        const def = defaults[k];

        // Arrays: applied if length differs (default arrays are empty)
        if (Array.isArray(value) && Array.isArray(def)) {
            if (value.length !== def.length) count++;
            continue;
        }

        // For primitives / nullables: applied if not strictly equal to default
        if (value !== def) count++;
    }

    return count;
};

// ---------------------------------------------------
// ---------------------- store ----------------------
// ---------------------------------------------------

/** Store factory */
const createTripFiltersState =
    (instanceId = 'trip-filters'): StateCreator<TripFiltersStoreType> =>
    (set, get) =>
        ({
            filters: createDefaultFilters(),
            appliedFiltersCount: 0,

            setFilters: (filters) => {
                set({ filters, appliedFiltersCount: getAppliedFiltersCount(filters, createDefaultFilters()) });
                logger.info('setFilters', { instanceId, filters });
            },
            updateListSection: (section, id, checked) => {
                set((currentState: TripFiltersState) => {
                    const newFilters = {
                        ...currentState.filters,
                        [section]: getUpdatedList(currentState.filters[section], id, checked),
                    };
                    return {
                        filters: newFilters,
                        appliedFiltersCount: getAppliedFiltersCount(newFilters, createDefaultFilters()),
                    };
                });
                logger.info('updateListSection', { instanceId, section, id, checked });
            },
            setFilter: (key, value) => {
                set((currentState: TripFiltersState) => {
                    const newFilters = { ...currentState.filters, [key]: value };
                    return {
                        filters: newFilters,
                        appliedFiltersCount: getAppliedFiltersCount(newFilters, createDefaultFilters()),
                    };
                });
                logger.info('setFilter', { instanceId, key, value });
            },
            setUseFilters: (value) =>
                set((currentState: TripFiltersState) => {
                    const newFilters = {
                        ...currentState.filters,
                        useFilters: value,
                        ...(value ? {} : { tripCategory: null }),
                    };
                    return {
                        filters: newFilters,
                        appliedFiltersCount: getAppliedFiltersCount(newFilters, createDefaultFilters()),
                    };
                }),

            resetFilters: () => {
                const defaults = createDefaultFilters();
                set({ filters: defaults, appliedFiltersCount: 0 });
            },
            applyFilters: (callback) => callback?.(get().filters),
        }) as TripFiltersStoreType;

// ----------------------------------------------------------
// ---------------------- export store ----------------------
// ----------------------------------------------------------

/** Export hook */
export const useTripFiltersStore = create<TripFiltersStoreType>()(
    subscribeWithSelector(
        devtools(persist(createTripFiltersState(), { name: LocalStorageKeys.TRIP_FILTERS }), {
            name: 'trip-filters-store',
        }),
    ),
);
