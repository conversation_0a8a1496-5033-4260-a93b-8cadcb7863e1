import { create, type StateCreator } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';

import { logger } from '@/infrastructure/logging';
import type { GetTripDetailQueryParams, TripDetail } from '@/infrastructure/api/trips/types';
import { tripsService } from '@/infrastructure/api/trips/trips.service';

export type TripDetailStoreType = TripDetailState & TripDetailActions;

interface TripDetailState {
    tripDetail: TripDetail | null;
    isLoading: boolean;
}

const DEFAULT_STATE: TripDetailState = {
    tripDetail: null,
    isLoading: false,
};

interface TripDetailActions {
    loadTripDetail: (params?: Partial<GetTripDetailQueryParams>) => Promise<void>;
}

const _tripDetailStore = (instanceId: string): StateCreator<TripDetailStoreType> => {
    return (set): TripDetailStoreType => ({
        ...DEFAULT_STATE,

        loadTripDetail: async (params?: Partial<GetTripDetailQueryParams>) => {
            set((state: TripDetailState) => ({ ...state, isLoading: true }));
            try {
                const res = await tripsService.getTripDetail({ ...params } as GetTripDetailQueryParams);

                set((state: TripDetailState) => ({
                    ...state,
                    tripDetail: res,
                    isLoading: false,
                }));

                logger.info(`tripDetailStore(${instanceId}): loadTripDetail: status update to `, res ?? {});
            } catch (error) {
                logger.error(`tripDetailStore(${instanceId}): loadTripDetail: error: `, error as Error);
                set((state: TripDetailState) => ({ ...state, isLoading: false }));
            }
        },
    });
};

export const useTripDetailStore = create<TripDetailStoreType>()(
    subscribeWithSelector(devtools(_tripDetailStore('main'), { name: 'trip-detail-store' })),
);
