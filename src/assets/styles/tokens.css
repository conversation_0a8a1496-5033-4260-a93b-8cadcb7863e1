@import 'tailwindcss';

@theme {
    /* Colors */
    --color-primary-50: rgba(241, 252, 246, 1);
    --color-primary-100: rgba(221, 251, 236, 1);
    --color-primary-200: rgba(190, 244, 218, 1);
    --color-primary-300: rgba(138, 235, 190, 1);
    --color-primary-400: rgba(80, 216, 154, 1);
    --color-primary-500: rgba(41, 190, 122, 1);
    --color-primary-600: rgba(28, 157, 98, 1);
    --color-primary-700: rgba(27, 131, 84, 1);
    --color-primary-800: rgba(25, 98, 66, 1);
    --color-primary-900: rgba(23, 80, 56, 1);
    --color-primary-950: rgba(7, 44, 28, 1);

    --color-secondary-50: rgba(238, 252, 253, 1);
    --color-secondary-100: rgba(211, 243, 250, 1);
    --color-secondary-200: rgba(172, 231, 245, 1);
    --color-secondary-300: rgba(115, 211, 237, 1);
    --color-secondary-400: rgba(50, 183, 222, 1);
    --color-secondary-500: rgba(23, 159, 202, 1);
    --color-secondary-600: rgba(21, 123, 165, 1);
    --color-secondary-700: rgba(24, 100, 134, 1);
    --color-secondary-800: rgba(29, 82, 109, 1);
    --color-secondary-900: rgba(28, 69, 93, 1);
    --color-secondary-950: rgba(13, 44, 63, 1);

    --color-neutral-50: rgba(255, 255, 255, 1);
    --color-neutral-100: rgba(239, 239, 239, 1);
    --color-neutral-200: rgba(220, 220, 220, 1);
    --color-neutral-300: rgba(189, 189, 189, 1);
    --color-neutral-400: rgba(152, 152, 152, 1);
    --color-neutral-500: rgba(124, 124, 124, 1);
    --color-neutral-600: rgba(101, 101, 101, 1);
    --color-neutral-700: rgba(82, 82, 82, 1);
    --color-neutral-800: rgba(70, 70, 70, 1);
    --color-neutral-900: rgba(61, 61, 61, 1);
    --color-neutral-950: rgba(41, 41, 41, 1);

    --color-error-500: rgba(228, 81, 79, 1);
    --color-success-500: rgba(78, 153, 102, 1);
    --color-warning-500: rgba(249, 120, 22, 1);

    /* ============================== */
    /* ============================== */
    /* ============================== */

    /* Font families */
    --font-family-sans: 'IBM Plex Sans Arabic', Helvetica, sans-serif;

    /* ============================== */
    /* ============================== */
    /* ============================== */

    /* Font weights */
    --font-weight-black: 900;
    --font-weight-extrabold: 800;
    --font-weight-bold: 700;
    --font-weight-semibold: 600;
    --font-weight-medium: 500;
    --font-weight-regular: 400;
    --font-weight-light: 300;

    /* Font styles */
    --font-style-normal: normal;

    /* Letter spacing (all were 0px in your file) */
    --letter-spacing-none: 0;

    /* Line-height percentages as multipliers (e.g. 150% → 1.5) */
    --line-height-150: 1.5;
    --line-height-108px: 6.75rem; /* original absolute px from Figma */

    /* ✨ DISPLAY (72px) */
    --font-display-size: 4.5rem; /* 72px */
    --font-display-line-height: 6.75rem; /* 108px */
    --font-display-letter-spacing: var(--letter-spacing-none);

    /* H1 (60px) */
    --font-h1-size: 3.75rem;
    --font-h1-line-height: 5.625rem; /* 90px */
    --font-h1-letter-spacing: var(--letter-spacing-none);

    /* H2 (48px) */
    --font-h2-size: 3rem;
    --font-h2-line-height: var(--line-height-150); /* 150% */
    --font-h2-letter-spacing: var(--letter-spacing-none);

    /* H3 (40px) */
    --font-h3-size: 2.5rem;
    --font-h3-line-height: var(--line-height-150);
    --font-h3-letter-spacing: var(--letter-spacing-none);

    /* H4 (32px) */
    --font-h4-size: 2rem;
    --font-h4-line-height: var(--line-height-150);
    --font-h4-letter-spacing: var(--letter-spacing-none);

    /* H5 (24px) */
    --font-h5-size: 1.5rem;
    --font-h5-line-height: var(--line-height-150);
    --font-h5-letter-spacing: var(--letter-spacing-none);

    /* H6 (20px) */
    --font-h6-size: 1.25rem;
    --font-h6-line-height: var(--line-height-150);
    --font-h6-letter-spacing: var(--letter-spacing-none);
}
