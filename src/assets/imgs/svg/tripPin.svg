<svg width="68" height="99" viewBox="0 0 68 99" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_dd_89_3919)">
<path d="M63.1577 31.5413C63.1577 47.8565 49.9501 70.4821 33.6577 70.4821C17.3653 70.4821 4.15771 47.8565 4.15771 31.5413C4.15771 15.2261 17.3653 2 33.6577 2C49.9501 2 63.1577 15.2261 63.1577 31.5413Z" fill="#1B8354" fill-opacity="0.5" shape-rendering="crispEdges"/>
</g>
<rect x="8.37201" y="6" width="50.5714" height="50.5714" rx="25.2857" fill="#1B8354"/>
<path d="M20.3244 24.9523C19.7721 24.9523 19.3244 25.4 19.3244 25.9523C19.3244 26.5046 19.7721 26.9523 20.3244 26.9523H28.3244C28.8767 26.9523 29.3244 26.5046 29.3244 25.9523C29.3244 25.4 28.8767 24.9523 28.3244 24.9523H20.3244Z" fill="white"/>
<path d="M20.3244 28.9523C19.7721 28.9523 19.3244 29.4 19.3244 29.9523C19.3244 30.5046 19.7721 30.9523 20.3244 30.9523H25.6577C26.21 30.9523 26.6577 30.5046 26.6577 29.9523C26.6577 29.4 26.21 28.9523 25.6577 28.9523H20.3244Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M33.9181 21.7659C33.5042 21.6314 32.9579 21.619 31.391 21.619H20.3244C19.7721 21.619 19.3244 21.1713 19.3244 20.619C19.3244 20.0668 19.7721 19.619 20.3244 19.619L31.5764 19.619C32.8857 19.6185 33.7801 19.6181 34.5361 19.8638C35.7841 20.2693 36.8112 21.1445 37.4132 22.2856L39.4689 22.2856C40.397 22.2856 41.1672 22.2856 41.8015 22.3496C42.4695 22.4171 43.0677 22.5614 43.6324 22.8987C44.197 23.236 44.6078 23.6942 44.9839 24.2504C45.341 24.7785 45.7061 25.4567 46.1461 26.2738L47.8638 29.4639C47.9038 29.5353 47.9354 29.612 47.957 29.6928C47.9818 29.7847 47.9929 29.8784 47.991 29.9711V32.6906C47.9911 34.201 47.9911 35.4354 47.8601 36.4099C47.7231 37.4285 47.4268 38.3116 46.7218 39.0165C45.9684 39.7699 45.006 40.0605 43.8802 40.1838C43.4797 41.7746 42.0396 42.9523 40.3244 42.9523C38.646 42.9523 37.2311 41.8247 36.7958 40.2857H30.5197C30.0843 41.8247 28.6694 42.9523 26.991 42.9523C25.2759 42.9523 23.8357 41.7746 23.4352 40.1838C22.3094 40.0605 21.347 39.7699 20.5936 39.0165C19.6591 38.082 19.4366 36.826 19.3623 35.3355C19.3348 34.7839 19.7597 34.3144 20.3113 34.2869C20.8629 34.2594 21.3324 34.6843 21.3599 35.2359C21.4313 36.67 21.6439 37.2384 22.0078 37.6023C22.2858 37.8803 22.6834 38.07 23.4953 38.176C23.9655 36.6933 25.3528 35.6189 26.991 35.6189C28.6694 35.6189 30.0845 36.7467 30.5197 38.2857H36.7957C37.231 36.7467 38.646 35.6189 40.3244 35.6189C41.9626 35.6189 43.3499 36.6933 43.8201 38.176C44.6321 38.07 45.0296 37.8803 45.3076 37.6023C45.579 37.3309 45.7708 36.9402 45.8779 36.1434C45.9889 35.3178 45.991 34.2187 45.991 32.619V30.9524L40.568 30.9525C39.7296 30.9531 39.0808 30.9536 38.5246 30.7729C37.4084 30.4102 36.5332 29.535 36.1705 28.4188C35.9898 27.8626 35.9903 27.2138 35.991 26.3754L35.991 26.219C35.991 24.6522 35.9787 24.1059 35.8442 23.692C35.5475 22.7787 34.8314 22.0626 33.9181 21.7659ZM45.3169 28.9524L44.4087 27.2657C43.9392 26.3939 43.621 25.8054 43.3271 25.3707C43.0458 24.9548 42.8318 24.7501 42.6068 24.6157C42.3819 24.4814 42.1002 24.39 41.6006 24.3395C41.0785 24.2868 40.4095 24.2856 39.4193 24.2856H37.957C37.9916 24.7817 37.9914 25.3514 37.9911 26.0337L37.991 26.219C37.991 27.2896 38.0034 27.5876 38.0726 27.8007C38.2375 28.3081 38.6353 28.7059 39.1427 28.8708C39.3558 28.94 39.6539 28.9524 40.7244 28.9524H45.3169ZM25.3244 39.2856C25.3244 38.3651 26.0706 37.6189 26.991 37.6189C27.9115 37.6189 28.6577 38.3651 28.6577 39.2856C28.6577 40.2061 27.9115 40.9523 26.991 40.9523C26.0706 40.9523 25.3244 40.2061 25.3244 39.2856ZM38.6577 39.2856C38.6577 38.3651 39.4039 37.6189 40.3244 37.6189C41.2448 37.6189 41.991 38.3651 41.991 39.2856C41.991 40.2061 41.2448 40.9523 40.3244 40.9523C39.4039 40.9523 38.6577 40.2061 38.6577 39.2856Z" fill="white"/>
<circle opacity="0.5" cx="14.2327" cy="14.2327" r="14.2327" transform="matrix(0.866044 -0.499967 0.866044 0.499967 9.00537 84.7139)" fill="#1B8354"/>
<circle cx="9.9894" cy="9.9894" r="9.9894" transform="matrix(0.866044 -0.499967 0.866044 0.499967 16.0627 83.9155)" fill="#1B8354"/>
<defs>
<filter id="filter0_dd_89_3919" x="0.157715" y="0" width="67" height="74.4821" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_89_3919"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="effect1_dropShadow_89_3919" result="effect2_dropShadow_89_3919"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_89_3919" result="shape"/>
</filter>
</defs>
</svg>
