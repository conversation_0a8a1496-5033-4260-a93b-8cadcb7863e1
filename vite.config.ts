import path from 'path';
import tailwindcss from '@tailwindcss/vite';
import react from '@vitejs/plugin-react';
import { defineConfig } from 'vite';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';

// https://vite.dev/config/
export default defineConfig({
    plugins: [
        react(),
        tailwindcss(),
        createSvgIconsPlugin({
            iconDirs: [path.resolve(process.cwd(), 'src/assets/imgs/svg')],
            symbolId: 'icon-[name]',
            svgoOptions: {
                multipass: true,
                plugins: [
                    // نحذف fill, stroke, style داخلية عشان نقدر نلون بالـ CSS أو currentColor
                    {
                        name: 'removeAttrs',
                        params: { attrs: '(fill|stroke|style)' },
                    },
                    // نقدر نضيف أي plugins أخرى لو احتجنا
                ],
            },
        }),
    ],
    resolve: {
        alias: {
            '@': path.resolve(__dirname, './src'),
            '@imgs': path.resolve(__dirname, './src/assets/imgs'),
            '@ui': path.resolve(__dirname, './src/components/ui'),
            '@components': path.resolve(__dirname, './src/components'),
            '@hooks': path.resolve(__dirname, './src/shared/hooks'),
            '@lib': path.resolve(__dirname, './src/shared/lib'),
            '@stores': path.resolve(__dirname, './src/stores'),
            '@utils': path.resolve(__dirname, './src/shared/utils'),
            '@enums': path.resolve(__dirname, './src/shared/enums'),
        },
    },
});
