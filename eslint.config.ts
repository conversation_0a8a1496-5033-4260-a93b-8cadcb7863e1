// For more info, see https://github.com/storybookjs/eslint-plugin-storybook#configuration-flat-config-format
import storybook from "eslint-plugin-storybook";

// eslint.config.ts
import { defineConfig } from 'eslint/config';
import tsParser from '@typescript-eslint/parser';
import tsPlugin from '@typescript-eslint/eslint-plugin';
import pluginReact from 'eslint-plugin-react';
import pluginReactHooks from 'eslint-plugin-react-hooks';
import pluginJsxA11y from 'eslint-plugin-jsx-a11y';
import pluginImport from 'eslint-plugin-import';
import pluginPrettier from 'eslint-plugin-prettier';
import prettier from 'eslint-config-prettier';
import globals from 'globals';

export default defineConfig([
    pluginReact.configs.flat.recommended,
    {
        files: ['src/**/*.{js,jsx,ts,tsx}'],
        ignores: [
            'node_modules/**/*',
            'dist/**/*',
            'build/**/*',
            'coverage/**/*',
            'public/**/*',
            '*.config.{js,ts}',
            'vite.config.ts',
        ],
        languageOptions: {
            parser: tsParser,
            parserOptions: {
                ecmaVersion: 'latest',
                sourceType: 'module',
                ecmaFeatures: {
                    jsx: true,
                },
                project: './tsconfig.app.json', // For type-aware linting
            },
            globals: {
                ...globals.browser,
                ...globals.node,
            },
        },
        plugins: {
            '@typescript-eslint': tsPlugin as any,
            react: pluginReact,
            'react-hooks': pluginReactHooks,
            'jsx-a11y': pluginJsxA11y,
            import: pluginImport,
            prettier: pluginPrettier,
        },
        settings: {
            react: {
                version: 'detect',
            },
            'import/resolver': {
                typescript: {
                    project: './tsconfig.app.json', 
                },
            },
        },
        rules: {
            // ✅ Prettier enforcement
            'prettier/prettier': 'error',

            // ✅ TypeScript
            '@typescript-eslint/no-explicit-any': 'warn',
            '@typescript-eslint/no-non-null-assertion': 'warn',
            '@typescript-eslint/explicit-function-return-type': 'off',
            '@typescript-eslint/explicit-module-boundary-types': 'off',
            '@typescript-eslint/consistent-type-imports': 'warn',

            // ✅ Import rules (Airbnb-style)
            'import/order': [
                'warn',
                {
                    groups: ['builtin', 'external', 'internal', 'parent', 'sibling', 'index'],
                    'newlines-between': 'always',
                },
            ],
            'import/no-unresolved': 'error',
            'import/prefer-default-export': 'off',

            // ✅ React-specific
            'react/react-in-jsx-scope': 'off', // React 17+
            'react/function-component-definition': 'off',
            'react/jsx-props-no-spreading': 'off',
            'react/prop-types': 'off', // Using TypeScript

            // ✅ Hooks
            'react-hooks/rules-of-hooks': 'error',
            'react-hooks/exhaustive-deps': 'warn',

            // ✅ Accessibility (jsx-a11y)
            'jsx-a11y/anchor-is-valid': 'warn',

            // ✅ General JS rules (Airbnb-style)
            'no-console': 'warn',
            'no-unused-vars': 'off', // TS handles this
            '@typescript-eslint/no-unused-vars': ['warn', { argsIgnorePattern: '^_' }],
            'prefer-const': 'error',
            'no-var': 'error',
            'object-shorthand': ['error', 'always'],
            'no-multiple-empty-lines': ['warn', { max: 1 }],
        },
    },
    prettier,
]);
