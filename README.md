# React + TypeScript + Vite

This template provides a minimal setup to get <PERSON><PERSON> working in Vite with HMR and some ESLint rules.

```
    //--- CSS ---
    - CSS classes that start with '_' (like '_effect' class) mean this class is a global class
```

## Application Structure

```
src/
├── app/                       # Main application files
│   ├── App.tsx               # Root application component
│   └── App.css               # Application styles
├── components/                # Component library
│   ├── common/               # Shared/common components
│   │   ├── email-dialog/     # Email dialog component
│   │   │   └── email-dialog.tsx
│   │   ├── filter-button/    # Filter button component
│   │   │   ├── filter-button.tsx
│   │   │   └── filter-button.css
│   │   ├── google-map/       # Google map component
│   │   │   ├── google-map.tsx
│   │   │   └── google-map.css
│   │   ├── labeling-item/    # Labeling item component
│   │   │   ├── labeling-item.tsx
│   │   │   └── labeling-item.css
│   │   ├── line-chart/       # Line chart component
│   │   │   └── line-chart.tsx
│   │   ├── loader/           # Loading component
│   │   │   └── loader.tsx
│   │   ├── no-data/          # No data indicator component
│   │   │   └── no-data.tsx
│   │   ├── page-loader/      # Page loading component
│   │   │   ├── page-loader.tsx
│   │   │   └── page-loader.css
│   │   ├── page-not-found/   # 404 page component
│   │   │   └── page-not-found.tsx
│   │   ├── search-field/     # Search field component
│   │   │   └── search-field.tsx
│   │   ├── summary-card/     # Summary card component
│   │   │   └── summary-card.tsx
│   │   ├── switch-button/    # Switch button component
│   │   │   └── switch-button.tsx
│   │   ├── tooltip/          # Tooltip component
│   │   │   └── tooltip.tsx
│   │   └── ui/               # UI component library
│   │       ├── button.tsx
│   │       ├── calendar.tsx
│   │       ├── card.tsx
│   │       ├── chart.tsx
│   │       ├── dialog.tsx
│   │       ├── dropdown-menu.tsx
│   │       ├── input.tsx
│   │       ├── label.tsx
│   │       ├── popover.tsx
│   │       ├── resizable.tsx
│   │       ├── separator.tsx
│   │       ├── sheet.tsx
│   │       ├── skeleton.tsx
│   │       ├── switch.tsx
│   │       ├── tabs.tsx
│   │       ├── textarea.tsx
│   │       └── tooltip.tsx
│   └── features/             # Feature-specific components
│       ├── dashboard/        # Dashboard feature
│       │   └── dashboard.tsx
│       ├── layout/           # Layout components
│       │   ├── layout.tsx
│       │   ├── footer/       # Footer component
│       │   │   └── footer.tsx
│       │   └── navbar/       # Navigation bar
│       │       ├── alerts-button.tsx
│       │       ├── language-button.tsx
│       │       ├── navbar-items.tsx
│       │       ├── navbar.tsx
│       │       ├── navbar.css
│       │       └── user-details.tsx
│       ├── login/            # Login feature
│       │   ├── login.tsx
│       │   ├── login.css
│       │   └── login-form.tsx
│       ├── monitor-board/    # Monitor board feature
│       │   ├── monitor-board.tsx
│       │   ├── monitor-menu.tsx
│       │   └── menu-taps/    # Menu tabs
│       │       ├── alerts-tap.tsx
│       │       ├── routes-tap.tsx
│       │       └── search-tap.tsx
│       ├── my-ports/         # My ports feature
│       │   ├── my-ports.tsx
│       │   ├── my-ports-details.tsx
│       │   ├── details-table-section.tsx
│       │   └── map-section.tsx
│       ├── reports/          # Reports feature
│       │   └── alerts/       # Alerts reports
│       │       └── alerts.tsx
│       └── trip-information/ # Trip information feature
│           ├── trip-information.tsx
│           ├── trip-details/ # Trip details
│           │   └── trip-details.tsx
│           ├── trip-map/     # Trip map
│           │   └── trip-map.tsx
│           ├── trip-pings-view/ # Trip pings view
│           │   └── trip-pings-view.tsx
│           ├── trip-viewers-view/ # Trip viewers view
│           │   └── trip-viewers-view.tsx
│           ├── trip-warnings-view/ # Trip warnings view
│           │   └── trip-warnings-view.tsx
│           ├── trip-activities-report-view/ # Trip activities report
│           │   └── trip-activities-report-view.tsx
│           ├── trip-alerts-viewers-view/ # Trip alerts viewers
│           │   └── trip-alerts-viewers-view.tsx
│           ├── trip-events-report-view/ # Trip events report
│           │   └── trip-events-report-view.tsx
│           └── trip-movement-report-view/ # Trip movement report
│               └── trip-movement-report-view.tsx
├── i18n/                     # Internationalization
│   ├── index.ts              # i18n configuration
│   └── locales/              # Translation files
│       ├── ar.json           # Arabic translations
│       └── en.json           # English translations
├── infrastructure/           # Infrastructure layer
│   ├── api/                  # API services
│   │   ├── index.ts          # API configuration
│   │   ├── authService.ts    # Authentication service
│   │   └── trackerService.ts # Tracker service
│   ├── logging/              # Logging utilities
│   │   └── logger.ts
│   ├── signalr/              # SignalR integration
│   │   ├── enhancedSignalR.ts
│   │   ├── signalrManager.ts
│   │   └── signalrSimulator.ts
│   └── validation/           # Validation utilities
│       ├── index.ts
│       ├── schemas.ts
│       └── validator.ts
├── lib/                      # Utility libraries
│   └── utils.ts              # Utility functions
├── routes/                   # Application routing
│   ├── routes.tsx            # Main routes configuration
│   └── routeMapping.ts       # Route mapping utilities
├── shared/                   # Shared resources
│   ├── hooks/                # Custom React hooks
│   │   ├── use-mobile.ts     # Mobile detection hook
│   │   ├── useBreadcrumbs.ts # Breadcrumbs hook
│   │   └── useTranslations.ts # Translations hook
│   └── types/                # TypeScript type definitions
│       ├── index.ts          # Common types
│       ├── google-maps.d.ts  # Google Maps types
│       └── api/              # API types
│           └── trackerApi.ts
├── stores/                   # State management
│   ├── authStore.ts          # Authentication store
│   ├── dashboardStore.ts     # Dashboard store
│   ├── filterStore.ts        # Filter store
│   └── localizationStore.ts  # Localization store
├── assets/                   # Static assets
│   ├── imgs/                 # Image assets
│   │   ├── language/         # Language flag images
│   │   │   ├── arabic.png
│   │   │   └── english.png
│   │   ├── 404.avif
│   │   ├── avatar.png
│   │   ├── charging.svg
│   │   ├── company-logo.png
│   │   ├── empty.webp
│   │   ├── logo.png
│   │   ├── map.png
│   │   └── overlay.png
│   └── styles/               # Style assets
│       └── variables.css     # CSS variables
├── index.css                 # Global styles
├── main.tsx                  # Application entry point
└── vite-env.d.ts            # Vite environment types
```

<!-- -------------------------------------------------------- -->

### 1. Google Maps API Key

You need to set up a Google Maps API key. Add it to your environment variables:

```env
VITE_GOOGLE_MAPS_API_KEY=your_api_key_here
```

### 2. Enable Google Maps JavaScript API

In your Google Cloud Console:

1. Enable the "Maps JavaScript API"
2. Create credentials (API Key)
3. Restrict the API key to your domain for security
